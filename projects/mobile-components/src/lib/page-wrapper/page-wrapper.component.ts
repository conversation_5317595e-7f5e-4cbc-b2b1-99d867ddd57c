import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-page-wrapper',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <!-- Use Tailwind classes for layout and scrolling -->
    <div [class]="getWrapperClasses()" 
         [style.--background]="hasBackground ? 'var(--ion-color-base, #0072bc)' : 'transparent'">
      
      <!-- Header Section (optional) -->
      <div class="px-4 md:px-6 lg:px-8 py-3">
        <ng-content select="[header]"></ng-content>
      </div>
      
      <!-- Hero Section (optional) -->
      <div class="relative z-10 px-4 md:px-8 -mb-8" *ngIf="hasHero">
        <ng-content select="[hero]"></ng-content>
      </div>
      
      <!-- Main Content -->
      <div [class]="getContentClasses()">
        <div [class]="getContainerClasses()">
          <!-- Single Column Layout -->
          <div class="w-full flex flex-col flex-1 min-h-0 -mt-0 relative z-20 pb-10 md:pb-16" *ngIf="layout === 'single'">
            <ng-content></ng-content>
          </div>
          
          <!-- Two Column Layout -->
          <div class="grid gap-5 md:grid-cols-3 md:gap-6 lg:gap-7" *ngIf="layout === 'grid'">
            <div class="w-full md:col-span-2">
              <ng-content select="[main]"></ng-content>
            </div>
            <div class="w-full">
              <ng-content select="[sidebar]"></ng-content>
            </div>
          </div>
          
          <!-- Custom Layout -->
          <ng-content select="[custom]" *ngIf="layout === 'custom'"></ng-content>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./page-wrapper.component.scss']
})
export class PageWrapperComponent {
  @Input() showHeader: boolean = false;
  @Input() hasHero: boolean = false;
  @Input() hasBackground: boolean = true;
  @Input() containerSize: 'sm' | 'md' | 'lg' | 'xl' | 'full' = 'lg';
  @Input() layout: 'single' | 'grid' | 'custom' = 'single';
  @Input() scrollable: boolean = true;
  
  get isMobile(): boolean {
    return typeof window !== 'undefined' && window.innerWidth < 768;
  }

  getWrapperClasses(): string {
    const classes = [
      'relative flex flex-col w-full',
      // CRITICAL FIX: Don't constrain height when inside ion-content
      // Let content expand naturally and allow ion-content to handle scrolling
      'min-h-0',
      // Only add scrolling classes if NOT inside ion-content
      // When inside ion-content, let the parent handle scrolling
      this.scrollable && !this.isInsideIonContent()
        ? 'h-full overflow-y-auto overflow-x-hidden touch-pan-y'
        : '',
      this.hasBackground
        ? 'bg-gradient-to-br from-blue-600 to-blue-700'
        : '',
      !this.isMobile ? 'bg-gray-50' : ''
    ];

    return classes.filter(Boolean).join(' ');
  }

  /**
   * Detect if this component is inside an ion-content element
   * This helps avoid nested scrolling containers
   */
  private isInsideIonContent(): boolean {
    if (typeof window === 'undefined') return false;

    // In most cases, if we're in an Ionic app with ion-content, we should let ion-content handle scrolling
    // This is a simple heuristic - we could make it more sophisticated if needed
    return true; // For now, assume we're always inside ion-content in this app
  }

  getContentClasses(): string {
    const classes = [
      'relative z-20 w-full flex-1 flex flex-col',
      'px-4 py-4',
      'md:px-6 md:py-6',
      'lg:px-8 lg:py-7'
    ];
    
    return classes.join(' ');
  }

  getContainerClasses(): string {
    const sizeClasses = {
      'sm': 'max-w-2xl',
      'md': 'max-w-3xl', 
      'lg': 'max-w-4xl',
      'xl': 'max-w-5xl',
      'full': 'max-w-full'
    };
    
    const classes = [
      'w-full mx-auto flex flex-col items-stretch flex-1 min-h-0',
      sizeClasses[this.containerSize] || sizeClasses.lg
    ];
    
    return classes.join(' ');
  }
}