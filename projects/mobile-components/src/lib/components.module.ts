import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { AddressComponent } from './address/address.component';
import { LoadingComponent } from './loading/loading.component';
import { PinComponent } from './pin/pin.component';
import { HeadLogoComponent } from './head-logo/head-logo.component';
import { StoresComponent } from './stores/stores.component';
import { CustomizerComponent } from './customizer/customizer.component';
// Removed NgxColorPickerWrapperModule; use standalone imports in components instead
import { ColoursComponent } from './colours/colours.component';
import { LayoutComponent } from './layout/layout.component';
import { InfoComponent } from './info/info.component';
import { ContactComponent } from './contact/contact.component';
import { SocialsPlainComponent } from './socials/plain/socials-plain.component';
import { SocialsCustomizerComponent } from './socials/customizer/socials-customizer.component';
import { PagesCustomizerComponent } from './pages/customizer/pages-customizer.component';
import { PagesLandingCustomizerComponent } from './pages/landing/customizer/pages-landing-customizer.component';
import { PagesLandingTheme1Component } from './pages/landing/themes/theme1/pages-landing-theme1.component';
import { PagesLoginCustomizerComponent } from './pages/login/customizer/pages-login-customizer.component';
import { PagesLoginTheme1Component } from './pages/login/themes/theme1/pages-login-theme1.component';
import { AccountPoolComponent } from './account-pool/account-pool.component';
import { AccountPoolInviteComponent } from './account-pool-invite/account-pool-invite.component';
import { PointsTransferComponent } from './points-transfer/points-transfer.component';
import { PointsTransferHistoryComponent } from './points-transfer-history/points-transfer-history.component';
import { PageWrapperComponent } from './page-wrapper/page-wrapper.component';
import { UiCardComponent } from './ui-card/ui-card.component';
import { UiButtonDirective } from './ui-button/ui-button.directive';
import { ProgramSelectionModule } from './program-selection/program-selection.module';
import { OnboardingModule } from './onboarding/onboarding.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { FormsModule as CustomFormsModule } from './forms/forms.module';
import { CardsModule } from './cards/cards.module';
import { UiModule } from './ui/ui.module';
import { ProductsPageComponent } from './products-page/products-page.component';
import { NotificationSettingsPageComponent } from './notification-settings-page/notification-settings-page.component';


@NgModule({
  declarations: [
    // Only non-standalone components should be declared here
  ],
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    // Import standalone components that were moved from declarations
    LoadingComponent,
    PinComponent,
    HeadLogoComponent,
    CustomizerComponent,
    ColoursComponent,
    LayoutComponent,
    InfoComponent,
    ContactComponent,
    SocialsPlainComponent,
    SocialsCustomizerComponent,
    PagesLandingCustomizerComponent,
    PagesLandingTheme1Component,
    PagesLoginCustomizerComponent,
    PagesLoginTheme1Component,
    AddressComponent,
    StoresComponent,
    AccountPoolComponent,
    AccountPoolInviteComponent,
    // Import standalone components
    PagesCustomizerComponent,
    PageWrapperComponent,
    UiCardComponent,
    UiButtonDirective,
    // Import program selection module
    ProgramSelectionModule,
    // Import onboarding module
    OnboardingModule,
    // Import new modular components
    DashboardModule,
    CustomFormsModule,
    CardsModule,
    UiModule,
    // Import standalone page components
    ProductsPageComponent,
    NotificationSettingsPageComponent,

  ],
  exports: [
    // Color picker provided via standalone imports in components
    AddressComponent,
    LoadingComponent,
    PinComponent,
    HeadLogoComponent,
    StoresComponent,
    CustomizerComponent,
    AccountPoolComponent,
    ColoursComponent,
    LayoutComponent,
    InfoComponent,
    ContactComponent,
    PagesLandingTheme1Component,
    PagesLoginTheme1Component,
    PagesLandingCustomizerComponent,
    PagesLoginCustomizerComponent,
    PagesCustomizerComponent,
    AccountPoolComponent,
    AccountPoolInviteComponent,
    // Export design system components
    PageWrapperComponent,
    UiCardComponent,
    UiButtonDirective,
    // Export program selection module
    ProgramSelectionModule,
    // Export onboarding module
    OnboardingModule,
    // Export new modular components
    DashboardModule,
    CustomFormsModule,
    CardsModule,
    UiModule,
    // Export standalone page components
    ProductsPageComponent,
    NotificationSettingsPageComponent,

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ComponentsModule {}
