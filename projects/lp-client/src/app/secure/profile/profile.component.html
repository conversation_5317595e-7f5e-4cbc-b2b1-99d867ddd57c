<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile ? (profile.givenNames || '') + ' ' + (profile.surname || '') : ''"
      [membership]="profile?.newMembershipNumber"
      type="profile"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>
  
  <!-- Profile Content -->
  <div class="profile-content">

  <form [formGroup]="profileForm" (ngSubmit)="doLoad()">
      <!-- Personal Information Section -->
      <lib-form-section 
        title="Personal Information"
        icon="person-outline"
        [isCollapsible]="false">
        
        <lib-form-grid>
          <!-- Title -->
          <lib-form-select
            label="Title"
            icon="ribbon-outline"
            [options]="getCodeList('TITL') | async"
            optionValue="codeId"
            optionLabel="description"
            [control]="form.title">
          </lib-form-select>

          <!-- First Name -->
          <lib-form-field
            label="First Name"
            icon="person-outline"
            type="text"
            [required]="true"
            [readonly]="status_loading"
            [control]="form.givenNames">
          </lib-form-field>

          <!-- Surname -->
          <lib-form-field
            label="Surname"
            icon="person-outline"
            type="text"
            [required]="true"
            [readonly]="status_loading"
            [control]="form.surname">
          </lib-form-field>

          <!-- Gender -->
          <lib-form-select
            label="Gender"
            icon="male-female-outline"
            [options]="getCodeList('SEX') | async"
            optionValue="codeId"
            optionLabel="description"
            [control]="form.gender">
          </lib-form-select>

          <!-- Birth Date -->
          <lib-form-field
            label="Birth Date"
            icon="calendar-outline"
            type="date"
            [max]="todaysDate12YearsAgo()"
            [control]="form.birthDate">
          </lib-form-field>
        </lib-form-grid>
      </lib-form-section>

      <!-- Identification Section -->
      <lib-form-section
        title="Identification"
        icon="id-card-outline"
        [isCollapsible]="false">
        
        <lib-form-grid>
          <!-- ID Type -->
          <div class="full-width">
            <lib-form-field
              label="ID Type"
              icon="id-card-outline"
              type="text"
              [value]="idType === 'nationalId' ? 'South African ID' : 'Passport'"
              [readonly]="true">
            </lib-form-field>
          </div>

          <!-- National ID -->
          <div class="full-width" *ngIf="idType === 'nationalId' || !idType">
            <lib-form-field
              label="South African ID Number"
              icon="id-card-outline"
              type="text"
              [readonly]="true"
              [control]="form.nationalIdNum">
            </lib-form-field>
          </div>

          <!-- Passport Fields -->
          <lib-form-field
            *ngIf="idType === 'passport'"
            label="Passport Number"
            icon="document-text-outline"
            type="text"
            [readonly]="true"
            [control]="form.passortNum">
          </lib-form-field>

          <lib-form-field
            *ngIf="idType === 'passport'"
            label="Country of Origin"
            icon="flag-outline"
            type="text"
            [value]="selectedCountryName"
            [readonly]="true"
            [required]="true">
          </lib-form-field>

          <lib-form-field
            *ngIf="idType === 'passport'"
            label="Passport Expiry Date"
            icon="calendar-outline"
            type="date"
            [min]="todaysDate()"
            [readonly]="true"
            [required]="true"
            [control]="form.expiryDate">
          </lib-form-field>
        </lib-form-grid>
      </lib-form-section>

      <!-- Contact Information Section -->
      <lib-form-section
        title="Contact Information"
        icon="call-outline"
        [isCollapsible]="false">
        
        <!-- Address Component -->
        <div class="address-section">
          <lp-pos-address
            type="POST"
            [mainAddress]="addr"
            [mainForm]="profileForm"
            #address_post
            [required_field]="true"
          ></lp-pos-address>
        </div>
        
        <lib-form-grid>
          <!-- Mobile Number -->
          <lib-form-field
            label="Mobile Number"
            icon="phone-portrait-outline"
            type="text"
            [value]="phoneTogether"
            [required]="true"
            [disabled]="true"
            [control]="form.phone">
          </lib-form-field>

          <!-- Email -->
          <lib-form-field
            label="Email Address"
            icon="mail-outline"
            type="email"
            [control]="form.emailAddress">
          </lib-form-field>
        </lib-form-grid>
        
        <!-- Favorite Store -->
        <div class="store-section">
          <lib-stores
            [favourite_id]="favourite_id"
            (updateDataEvent)="updateAddress($event)"
            [required_field]="true"
          />
        </div>
      </lib-form-section>
      <!-- Save Button -->
      <div class="action-section">
        <ion-button 
          expand="block" 
          class="save-button" 
          type="submit"
          [disabled]="status_loading"
        >
          <ion-icon name="save-outline" slot="start"></ion-icon>
          Save Profile
        </ion-button>
      </div>
    </form>
  </div>
</lib-page-wrapper>
