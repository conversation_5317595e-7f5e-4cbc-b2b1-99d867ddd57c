<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile ? (profile.givenNames || '') + ' ' + (profile.surname || '') : ''"
      [membership]="profile?.newMembershipNumber"
      type="profile"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

  <div class="program-selection-content ion-padding">
  <!-- Header Content -->
  <ion-card class="header-card">
    <ion-card-content>
      <div class="page-title">
        <h1>Select Your Programs</h1>
        <p>Choose the loyalty programs that best fit your lifestyle and interests.</p>
      </div>
      
      <!-- Progress indicator -->
      <div class="progress-section">
        <div class="progress-bar">
          <div class="progress-fill" style="width: 50%"></div>
        </div>
        <span class="progress-text">Step 2 of 4</span>
      </div>
    </ion-card-content>
  </ion-card>

  <!-- Loading State -->
  <div *ngIf="isLoading && !hasError" class="loading-container">
    <div class="loading-content">
      <ion-spinner></ion-spinner>
      <p>Loading programs...</p>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="hasError" class="error-container">
    <ion-card class="error-card">
      <ion-card-content>
        <div class="error-content">
          <ion-icon name="alert-circle" color="danger" size="large"></ion-icon>
          <h3>Oops! Something went wrong</h3>
          <p>{{ errorMessage }}</p>
          <ion-button (click)="retry()" color="primary" fill="solid">
            <ion-icon name="refresh" slot="start"></ion-icon>
            Try Again
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && !hasError" class="main-content">
    <!-- Category Filter -->
    <ion-card class="filter-card" *ngIf="categories.length > 0">
      <ion-card-content>
        <h3>Filter by Category</h3>
        <div class="category-chips">
          <ion-chip 
            [class.selected]="currentCategory === null"
            (click)="filterByCategory(null)">
            <ion-icon name="apps"></ion-icon>
            <ion-label>All Programs</ion-label>
          </ion-chip>
          
          <ion-chip 
            *ngFor="let category of categories"
            [class.selected]="currentCategory === category.id"
            (click)="filterByCategory(category.id)">
            <ion-icon [name]="getCategoryIcon(category.id)"></ion-icon>
            <ion-label>{{ category.name | titlecase }}</ion-label>
          </ion-chip>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Programs List -->
    <ion-card class="programs-section-card">
      <ion-card-header>
        <div class="programs-header">
          <h3>Available Programs</h3>
          <ion-badge class="count-badge">{{ selectedPrograms.size }} selected</ion-badge>
        </div>
      </ion-card-header>
      <ion-card-content>
        <div class="programs-grid">
          <div 
            *ngFor="let program of filteredPrograms" 
            class="program-item"
            [class.selected]="isProgramSelected(program.id)"
            [class.required]="program.isRequired"
            (click)="toggleProgramSelection(program)">
            
            <div class="program-header">
              <div class="program-icon-wrapper">
                <ion-icon [name]="getProgramIcon(program.id)" class="program-icon"></ion-icon>
              </div>
              <div class="program-info">
                <h3 class="program-name">{{ program.name }}</h3>
                <div class="program-badges">
                  <span class="category-badge" [class.rewards]="program.category.id === 'rewards'">
                    {{ program.category.name }}
                  </span>
                  <span class="required-badge" *ngIf="program.isRequired">
                    Required
                  </span>
                </div>
              </div>
              <div class="selection-indicator">
                <ion-checkbox 
                  [checked]="isProgramSelected(program.id)"
                  [disabled]="program.isRequired"
                  (click)="$event.stopPropagation()"
                  (ionChange)="toggleProgramSelection(program)">
                </ion-checkbox>
              </div>
            </div>

            <div class="program-content">
              <p class="program-description">{{ program.description }}</p>

            <!-- Benefits List -->
            <div class="benefits-section" *ngIf="program.benefits && program.benefits.length > 0">
              <h4>Benefits:</h4>
              <ul class="benefits-list">
                <li *ngFor="let benefit of program.benefits">
                  <ion-icon name="checkmark-circle" color="success" size="small"></ion-icon>
                  {{ benefit }}
                </li>
              </ul>
            </div>

            <!-- Eligibility Information -->
            <div class="eligibility-section" *ngIf="program.eligibilityCriteria && program.eligibilityCriteria.length > 0">
              <div class="eligibility-toggle" (click)="toggleEligibility(program)">
                <span>Eligibility Requirements</span>
                <ion-icon [name]="program.showEligibility ? 'chevron-up' : 'chevron-down'"></ion-icon>
              </div>
              <div *ngIf="program.showEligibility" class="eligibility-content">
                <ul class="eligibility-list">
                  <li *ngFor="let criterion of program.eligibilityCriteria">
                    <ion-icon name="information-circle" color="medium" size="small"></ion-icon>
                    {{ criterion }}
                  </li>
                </ul>
              </div>
            </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="filteredPrograms.length === 0" class="empty-state">
          <ion-icon name="search-outline" class="empty-icon"></ion-icon>
          <h3>No programs found</h3>
          <p>Try adjusting your category filter to see more programs.</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Bottom Action Bar -->
  <div class="bottom-actions" *ngIf="!isLoading && !hasError">
    <div class="action-content">
      <div class="selection-summary">
        <span class="selected-count">{{ selectedPrograms.size }} programs selected</span>
        <span class="required-note" *ngIf="hasRequiredPrograms()">
          * Required programs are automatically included
        </span>
      </div>
      <ion-button 
        (click)="continue()" 
        [disabled]="!canContinue()"
        expand="block" 
        size="large" 
        color="primary">
        Continue to Onboarding
        <ion-icon name="arrow-forward" slot="end"></ion-icon>
      </ion-button>
    </div>
  </div>
  </div>
</lib-page-wrapper>