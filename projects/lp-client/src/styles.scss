/* Use Design System */
@use './styles/design-system/index';

/* Base app structure - simplified for mobile compatibility */
html, body, ion-app, .ion-page, ion-router-outlet {
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
}

html {
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
}

body {
  height: 100%;
  min-height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  /* Allow natural scrolling - remove overflow hidden */
  overflow: auto;
}

ion-app {
  height: 100%;
  display: block;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure ion-content handles scrolling properly */
ion-content {
  --overflow: auto; /* Let content define need for scroll */
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  margin: 0 !important;
  padding: 0 !important;
  
  /* Enable smooth scrolling on iOS */
  &::part(scroll) {
    -webkit-overflow-scrolling: touch;
  }
  
  /* CRITICAL: Ensure the scroll content can expand */
  .scroll-content {
    min-height: auto !important;
    height: auto !important;
    contain: none !important;
  }
  
  /* Also target the inner scroll element if it exists */
  .inner-scroll {
    min-height: auto !important;
    height: auto !important;
  }
}

/* Ensure the main layout is a flex column that fills the viewport */
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix the main app content scroll container */
#app-content {
  flex: 1;
  min-height: 0;
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  margin: 0 !important;
  padding: 0 !important;
  
  /* The wrapper div inside ion-content should not have fixed height */
  > div {
    height: auto !important;
    min-height: 100%;
    overflow: visible !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* Ensure ion-router-outlet displays content properly */
/* Only apply to the main router outlet, not nested ones */
/* The main router-outlet should be a block element and fill available space */
.main-layout > ion-content > div > ion-router-outlet {
  display: block;
  min-height: 100%;
  height: auto;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure ion-router-outlet has no default spacing */
ion-router-outlet {
  margin: 0 !important;
  padding: 0 !important;
}

/* Nested router outlets should display normally */
ion-router-outlet ion-router-outlet {
  display: block;
  flex: 1 1 auto;
  min-height: 0;
  height: auto;
  margin: 0 !important;
  padding: 0 !important;
}

/* Override Ionic's default padding on headers and toolbars */
ion-header, ion-toolbar {
  margin: 0 !important;
  padding: 0 !important;
}

ion-toolbar {
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
}

