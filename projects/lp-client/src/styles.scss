/* Use Design System */
@use './styles/design-system/index';

/* Base app structure - simplified for mobile compatibility */
html, body, ion-app, .ion-page {
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
}

/* CRITICAL FIX: Don't constrain ion-router-outlet height */
ion-router-outlet {
  height: auto !important;
  min-height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
}

html {
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
}

body {
  height: 100%;
  min-height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  /* Allow natural scrolling - remove overflow hidden */
  overflow: auto;
}

ion-app {
  height: 100%;
  display: block;
  margin: 0 !important;
  padding: 0 !important;
}

/* CRITICAL FIX: Ensure ion-content handles scrolling properly */
ion-content {
  --overflow: auto; /* Let content define need for scroll */
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  margin: 0 !important;
  padding: 0 !important;

  /* Enable smooth scrolling on iOS */
  &::part(scroll) {
    -webkit-overflow-scrolling: touch;
  }

  /* CRITICAL: Ensure the scroll content can expand beyond viewport */
  .scroll-content {
    min-height: auto !important;
    height: auto !important;
    contain: none !important;
    /* Allow content to expand naturally */
    flex: none !important;
  }

  /* Also target the inner scroll element if it exists */
  .inner-scroll {
    min-height: auto !important;
    height: auto !important;
    /* Ensure inner scroll doesn't constrain content */
    flex: none !important;
  }

  /* CRITICAL: Fix for page-wrapper inside ion-content */
  lib-page-wrapper {
    /* Remove height constraints that prevent scrolling */
    height: auto !important;
    min-height: auto !important;

    /* Ensure the wrapper div inside page-wrapper can expand */
    > div {
      height: auto !important;
      min-height: auto !important;
      /* Remove any flex constraints that limit height */
      flex: none !important;
    }
  }
}

/* Ensure the main layout is a flex column that fills the viewport */
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100%;
  margin: 0 !important;
  padding: 0 !important;
}

/* CRITICAL FIX: Main app content scroll container */
#app-content {
  flex: 1;
  min-height: 0;
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  margin: 0 !important;
  padding: 0 !important;

  /* CRITICAL: The wrapper div inside ion-content should expand naturally */
  > div {
    height: auto !important;
    min-height: auto !important; /* Changed from 100% to auto */
    overflow: visible !important;
    margin: 0 !important;
    padding: 0 !important;
    /* Remove any flex constraints */
    flex: none !important;
  }

  /* Ensure ion-router-outlet doesn't constrain height */
  ion-router-outlet {
    height: auto !important;
    min-height: auto !important;
    display: block !important;

    /* Ensure routed components can expand */
    > * {
      height: auto !important;
      min-height: auto !important;
    }
  }
}

/* GLOBAL SCROLLING FIX: Ensure all pages can scroll properly */
/* This applies to all page components that might have scrolling issues */
app-onboarding-flow,
app-notification-settings,
app-profile,
app-transactions,
app-home,
[class*="page-"],
[class*="component-"] {
  /* Remove height constraints that prevent scrolling */
  height: auto !important;
  min-height: auto !important;

  /* Ensure content containers can expand */
  .content,
  .page-content,
  .main-content,
  ion-content,
  .ion-page {
    height: auto !important;
    min-height: auto !important;
    overflow: visible !important;
  }
}

/* CRITICAL: Fix for Ionic cards and content inside scrollable areas */
ion-card,
.section-card,
.card {
  /* Ensure cards don't constrain their content */
  height: auto !important;
  min-height: auto !important;

  ion-card-content {
    height: auto !important;
    min-height: auto !important;
  }
}

/* AGGRESSIVE FIX: Override all flex-1 constraints that prevent scrolling */
.flex-1 {
  /* When inside ion-content, don't constrain height */
  flex: none !important;
}

/* CRITICAL: Fix for Tailwind flex classes that prevent scrolling */
ion-content {
  .flex-1,
  .h-full,
  .min-h-screen,
  .min-h-full {
    /* Override height constraints inside scrollable containers */
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
  }

  /* Ensure flex containers don't constrain content */
  .flex {
    &.flex-col {
      height: auto !important;
      min-height: auto !important;
    }
  }
}

/* ULTIMATE SCROLLING FIX: Force all potentially problematic elements to allow scrolling */
/* This is an aggressive fix to ensure scrolling works everywhere */
ion-content,
ion-content *,
lib-page-wrapper,
lib-page-wrapper *,
.page-wrapper,
.page-wrapper * {
  /* Remove all height constraints that could prevent scrolling */
  max-height: none !important;

  /* Override any flex constraints */
  &.flex-1 {
    flex: none !important;
  }

  /* Override height constraints */
  &.h-full,
  &.min-h-full,
  &.min-h-screen {
    height: auto !important;
    min-height: auto !important;
  }
}

/* SPECIFIC FIX: Target the exact elements causing scrolling issues */
ion-content {
  /* Ensure ion-content itself can scroll */
  overflow-y: auto !important;
  overflow-x: hidden !important;

  /* Fix the scroll wrapper */
  .scroll-content,
  .scroll-y {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    overflow: visible !important;
  }

  /* Fix page-wrapper inside ion-content */
  lib-page-wrapper {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    overflow: visible !important;

    /* Fix all nested divs */
    div {
      height: auto !important;
      min-height: auto !important;
      max-height: none !important;

      &.flex-1 {
        flex: none !important;
      }
    }
  }
}

/* Ensure ion-router-outlet displays content properly */
/* Only apply to the main router outlet, not nested ones */
/* The main router-outlet should be a block element and fill available space */
.main-layout > ion-content > div > ion-router-outlet {
  display: block;
  min-height: 100%;
  height: auto;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure ion-router-outlet has no default spacing */
ion-router-outlet {
  margin: 0 !important;
  padding: 0 !important;
}

/* Nested router outlets should display normally */
ion-router-outlet ion-router-outlet {
  display: block;
  flex: 1 1 auto;
  min-height: 0;
  height: auto;
  margin: 0 !important;
  padding: 0 !important;
}

/* Override Ionic's default padding on headers and toolbars */
ion-header, ion-toolbar {
  margin: 0 !important;
  padding: 0 !important;
}

ion-toolbar {
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
}

