// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'DEV',
  client: 'rmic',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'Loyalty Demo',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://ffz1dev.loyaltyplus.aero/',
    logEndpoint: 'https://ffz1dev.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    termsConditions: 'http://payroll.dv.lss.si/servlet/systemImage',
    navigation: {
      sidebarTitle: 'Loyalty Plus',
      sidebarIcon: 'assets/images/logo.png',
      type: 'sidebar',
      routes: [
        // Main navigation items (shown in bottom nav)
        {
          path: '/public/home',
          label: 'Home',
          icon: 'home-outline',
          main: true,
          sidebar: true,
          more: false,
          exact: true
        },
        {
          path: '/public/virtual',
          label: 'Cards',
          icon: 'card-outline',
          main: true,
          sidebar: false,
          more: false,
          exact: false
        },
        {
          path: '/secure/transactions',
          label: 'Transactions',
          icon: 'receipt-outline',
          main: true,
          sidebar: true,
          more: false,
          exact: false
        },
        {
          path: '/public/games/home',
          label: 'Games',
          icon: 'game-controller-outline',
          main: true,
          sidebar: true,
          more: false,
          exact: false
        },
        // More menu items
        {
          path: '/secure/profile',
          label: 'Profile',
          icon: 'person-outline',
          main: false,
          sidebar: true,
          more: true,
          exact: false
        },
        {
          path: '/public/stores',
          label: 'Stores',
          icon: 'location-outline',
          main: false,
          sidebar: true,
          more: true,
          exact: false
        },
        {
          path: '/public/contactus',
          label: 'Contact Us',
          icon: 'call-outline',
          main: false,
          sidebar: true,
          more: true,
          exact: false
        },
        {
          path: '/secure/settings',
          label: 'Settings',
          icon: 'settings-outline',
          main: false,
          sidebar: true,
          more: true,
          exact: false
        },
        {
          path: '/secure/program-selection',
          label: 'Program Selection',
          icon: 'apps-outline',
          main: false,
          sidebar: true,
          more: true,
          exact: false
        },
        {
          path: '/secure/program-management',
          label: 'My Programs',
          icon: 'layers-outline',
          main: false,
          sidebar: true,
          more: true,
          exact: false
        },
        // Additional navigation items for sidebar
        {
          path: '/public/games/favourites',
          label: 'Favourites',
          icon: 'heart-outline',
          main: false,
          sidebar: true,
          more: false,
          exact: false
        },
        {
          path: '/secure/virtualcard',
          label: 'Virtual Card',
          icon: 'wallet-outline',
          main: false,
          sidebar: true,
          more: false,
          exact: false
        }
      ],
    },
    pages: [
      // PUBLIC PAGES (secure: false)
      // Landing and Home Pages (exactly matching lp-client structure)
      {
        title: 'landing',
        path: 'landing',
        secure: false,
        class: 'app-backgroundss',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              lssConfig: 'lssConfig'
            }
          },
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              profile: 'profile'
            }
          }
        ]
      },
      {
        title: 'home',
        path: 'home',
        secure: false,
        class: 'app-background',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc'
            }
          },
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              profile: 'profile'
            }
          }
        ]
      },
      {
        title: 'tab2',
        path: 'tab2',
        secure: false,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Tab 2',
              subtitle: 'Secondary navigation tab'
            }
          },
          {
            type: 'ButtonComponent',
            inputs: {
              text: 'Go to Home',
              expand: 'block',
              fill: 'solid',
              color: 'primary'
            }
          }
        ]
      },

      // Authentication Pages (exactly matching lp-client structure)
      {
        title: 'login',
        path: 'login',
        secure: false,
        class: 'app-backgrounds',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            inputs: {
              kc: 'kc',
              lssConfig: 'lssConfig'
            }
          }
        ]
      },
      {
        title: 'signup',
        path: 'signup',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Create Account',
              subtitle: 'Join the loyalty program'
            }
          },
          {
            type: 'SignupComponent',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router'
            }
          }
        ]
      },
      {
        title: 'otp',
        path: 'otp',
        secure: false,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'OTP Verification',
              subtitle: 'Enter the code sent to your device'
            }
          },
          {
            type: 'OtpValidatorComponent',
            inputs: {
              length: 6,
              autoSubmit: true
            }
          }
        ]
      },
      {
        title: 'validate',
        path: 'validate',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Card Validation',
              subtitle: 'Validate your loyalty card'
            }
          },
          {
            type: 'ValidateComponent',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router'
            }
          }
        ]
      },
      {
        title: 'password',
        path: 'password',
        secure: false,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Reset Password',
              subtitle: 'Enter your email to reset password'
            }
          },
          {
            type: 'InputComponent',
            inputs: {
              type: 'email',
              placeholder: 'Enter your email',
              label: 'Email Address'
            }
          },
          {
            type: 'ButtonComponent',
            inputs: {
              text: 'Send Reset Link',
              expand: 'block',
              fill: 'solid',
              color: 'primary'
            }
          }
        ]
      },

      // Games Pages (exactly matching lp-client structure)
      {
        title: 'games-home',
        path: 'games/home',
        secure: false,
        class: '',
        components: [
          {
            type: 'PagesLandingThemesGamesComponent',
            inputs: {}
          }
        ]
      },
      {
        title: 'games-categories',
        path: 'games/categories',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Game Categories',
              subtitle: 'Choose your favorite games'
            }
          },
          {
            type: 'CategoriesComponent',
            inputs: {
              config: 'games'
            }
          }
        ]
      },
      {
        title: 'games-single',
        path: 'games/single',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'GamesSingleComponent',
            inputs: {
              config: 'games'
            }
          }
        ]
      },
      {
        title: 'games-all',
        path: 'games/all',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'All Games',
              subtitle: 'Browse all available games'
            }
          },
          {
            type: 'AllGamesComponent',
            inputs: {
              config: 'games'
            }
          }
        ]
      },
      {
        title: 'games-dashboard',
        path: 'games/dashboard',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Games Dashboard',
              subtitle: 'Your gaming progress'
            }
          },
          {
            type: 'DashboardComponent',
            inputs: {
              config: 'games'
            }
          }
        ]
      },
      {
        title: 'games-favourites',
        path: 'games/favourites',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Favourite Games',
              subtitle: 'Your saved games'
            }
          },
          {
            type: 'FavouritesComponent',
            inputs: {
              config: 'games'
            }
          }
        ]
      },
      {
        title: 'games-how-to-play',
        path: 'games/how-to-play',
        secure: false,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'How to Play',
              subtitle: 'Game instructions and rules'
            }
          },
          {
            type: 'ParagraphComponent',
            inputs: {
              text: 'Welcome to our games section! Here you can play various games to earn loyalty points and rewards.',
              class: 'text-lg mb-4'
            }
          },
          {
            type: 'ButtonComponent',
            inputs: {
              text: 'Start Playing',
              expand: 'block',
              fill: 'solid',
              color: 'primary'
            }
          }
        ]
      },

      // Store and Shopping Pages
      {
        title: 'stores',
        path: 'stores',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'StoresPageComponent',
            inputs: {
              'profile': 'profile',
              'memberService': 'memberService',
              'partnerService': 'partnerService',
              'systemService': 'systemService',
              'kc': 'kc',
              'lssConfig': 'lssConfig'
            }
          }
        ]
      },
      {
        title: 'store-detail',
        path: 'store-detail',
        secure: false,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Store Details',
              subtitle: 'Store information and offers'
            }
          },
          {
            type: 'CardComponent',
            inputs: {
              title: 'Store Information',
              content: 'Detailed store information and current offers will be displayed here.'
            }
          }
        ]
      },
      {
        title: 'virtual',
        path: 'virtual',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Virtual Card',
              subtitle: 'Your digital loyalty card'
            }
          },
          {
            type: 'CardComponent',
            inputs: {
              title: 'Loyalty Card',
              content: 'Your virtual loyalty card will be displayed here.',
              class: 'loyalty-card'
            }
          }
        ]
      },

      // Communication Pages
      {
        title: 'chat',
        path: 'chat',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Chat',
              subtitle: 'Customer support chat'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Chat functionality will be available here.',
              type: 'info'
            }
          }
        ]
      },
      {
        title: 'notifications',
        path: 'notifications',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Notifications',
              subtitle: 'Your latest updates'
            }
          },
          {
            type: 'ListComponent',
            inputs: {
              items: [
                {
                  title: 'Welcome!',
                  subtitle: 'Thanks for joining our loyalty program',
                  icon: 'notifications-outline'
                }
              ]
            }
          }
        ]
      },

      // SECURE PAGES (secure: true)
      // User Account Pages
      {
        title: 'profile',
        path: 'profile',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Profile',
              subtitle: 'Manage your account'
            }
          },
          {
            type: 'ParagraphComponent',
            inputs: {
              text: 'Profile management features would be available here.'
            }
          }
        ]
      },
      {
        title: 'profile-details',
        path: 'profile-details',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Profile Details',
              subtitle: 'Update your information'
            }
          },
          {
            type: 'InputComponent',
            inputs: {
              type: 'text',
              placeholder: 'First Name',
              label: 'First Name'
            }
          },
          {
            type: 'InputComponent',
            inputs: {
              type: 'text',
              placeholder: 'Last Name',
              label: 'Last Name'
            }
          },
          {
            type: 'ButtonComponent',
            inputs: {
              text: 'Save Changes',
              expand: 'block',
              fill: 'solid',
              color: 'primary'
            }
          }
        ]
      },
      {
        title: 'profileremove',
        path: 'profileremove',
        secure: true,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Delete Account',
              subtitle: 'Permanently remove your account'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Warning: This action cannot be undone.',
              type: 'warning'
            }
          },
          {
            type: 'ButtonComponent',
            inputs: {
              text: 'Delete Account',
              expand: 'block',
              fill: 'solid',
              color: 'danger'
            }
          }
        ]
      },
      {
        title: 'security',
        path: 'security',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Security Settings',
              subtitle: 'Manage your account security'
            }
          },
          {
            type: 'ListComponent',
            inputs: {
              items: [
                {
                  title: 'Change Password',
                  subtitle: 'Update your password',
                  icon: 'lock-closed-outline'
                },
                {
                  title: 'Two-Factor Authentication',
                  subtitle: 'Enable 2FA for extra security',
                  icon: 'shield-checkmark-outline'
                }
              ]
            }
          }
        ]
      },

      // Financial Pages (matching lp-client structure)
      {
        title: 'transactions',
        path: 'transactions',
        secure: true,
        class: 'min-h-screen',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              balance: 'profile?.currentBalance',
              src: 'lssConfig.icon'
            }
          },
          {
            type: 'HeadingComponent',
            inputs: {
              text: 'Transaction History',
              class: 'page-title px-4 py-2'
            }
          },
          {
            type: 'TransactionsPageComponent',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig',
              apiBaseUrl: 'lssConfig.apiBaseUrl',
              productId: 'lssConfig.workflow?.productId'
            }
          }
        ]
      },
      {
        title: 'statements',
        path: 'statements',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'LibHeadLogo',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Statements',
              subtitle: 'Your financial statements'
            }
          },
          {
            type: 'StatementsPageComponent',
            inputs: {
              profile: 'profile',
              memberService: 'memberService',
              lssConfig: 'lssConfig',
              apiBaseUrl: 'lssConfig.apiBaseUrl'
            }
          }
        ]
      },
      {
        title: 'virtualcard',
        path: 'virtualcard',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              names: '$profile.givenNames + " " + $profile.surname',
              membership: '$profile.externalId',
              type: 'card', 
              balance: '$profile.currentBalance',
              src: 'assets/images/make-it.png'
            }
          },
          {
            type: 'VirtualCardPageComponent',
            inputs: {
              profile: 'profile',
              memberService: 'memberService',
              keyCloakService: 'keyCloakService',
              lssConfig: 'lssConfig',
              multiTenantContext: 'multiTenantContext',
              apiBaseUrl: 'lssConfig.apiBaseUrl',
              productId: 'lssConfig.workflow?.productId'
            }
          }
        ]
      },
      {
        title: 'dashboard',
        path: 'dashboard',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Welcome Back',
              subtitle: 'Your secure dashboard'
            }
          },
          {
            type: 'ParagraphComponent',
            inputs: {
              text: 'Dashboard features would be available here.'
            }
          }
        ]
      },

      // Settings Pages
      {
        title: 'settings',
        path: 'settings',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'SettingsPageComponent',
            inputs: {
              profile: 'profile'
            }
          }
        ]
      },
      {
        title: 'notification-settings',
        path: 'notification-settings',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Notification Settings',
              subtitle: 'Manage your notifications'
            }
          },
          {
            type: 'CheckboxComponent',
            inputs: {
              label: 'Email Notifications',
              checked: true
            }
          },
          {
            type: 'CheckboxComponent',
            inputs: {
              label: 'Push Notifications',
              checked: true
            }
          }
        ]
      },
      {
        title: 'notification-admin',
        path: 'notification-admin',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Notification Admin',
              subtitle: 'Admin notification management'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Admin notification management interface.',
              type: 'info'
            }
          }
        ]
      },
      {
        title: 'notification-analytics',
        path: 'notification-analytics',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/logo.png',
              title: 'Notification Analytics',
              subtitle: 'Notification performance metrics'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Notification analytics and metrics will be displayed here.',
              type: 'info'
            }
          }
        ]
      },

      // Program Selection Pages
      {
        title: 'program-selection',
        path: 'program-selection',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'ProgramSelectionPageComponent',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig',
              programService: 'programService',
              onboardingService: 'onboardingService',
              memberService: 'memberService',
              keyCloakService: 'keyCloakService',
              router: 'router',
              programMemberService: 'programMemberService',
              config: {
                isMultiTenant: false,
                allowMultipleSelection: true,
                showProgress: true,
                progressStep: 2,
                progressTotal: 4,
                title: 'Select Your Programs',
                subtitle: 'Choose the loyalty programs that best fit your lifestyle and interests.',
                continueButtonText: 'Continue to Onboarding',
                navigationTarget: '/secure/onboarding-flow'
              }
            }
          }
        ]
      },

      // Program Management Page
      {
        title: 'program-management',
        path: 'program-management',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'ProgramManagementPageComponent',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig',
              programService: 'programService',
              onboardingService: 'onboardingService',
              memberService: 'memberService',
              keyCloakService: 'keyCloakService',
              router: 'router',
              multiTenantContextService: 'multiTenantContextService',
              config: {
                title: 'My Programs',
                subtitle: 'Manage your program enrollments and explore available programs',
                showSummaryCards: true,
                showSearch: true,
                showCategoryFilter: true,
                allowEnrollment: true,
                allowUnenrollment: true,
                allowProgramEntry: true,
                defaultViewMode: 'grid'
              }
            }
          }
        ]
      },

      // Support Pages
      {
        title: 'contactus',
        path: 'contactus',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Contact Us',
              subtitle: 'Get in touch with Make It With Mica support'
            }
          },
          {
            type: 'ContactusComponent',
            inputs: {
              categoryCode: 'CNCT',
              categories: ['General', 'Complaint']
            }
          }
        ]
      },
      // Add app/stores page for legacy route compatibility
      {
        title: 'app-stores',
        path: 'app/stores',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'StoresPageComponent',
            inputs: {
              'profile': 'profile',
              'memberService': 'memberService',
              'partnerService': 'partnerService',
              'systemService': 'systemService',
              'kc': 'kc',
              'lssConfig': 'lssConfig'
            }
          }
        ]
      }
    ],
    contact: {
      callCenter: '************',
      email: '',
    },
    socials: {
      facebook: 'https://www.facebook.com/micahardware',
      twitter: 'https://twitter.com/micahardware',
      linkedin:
        'https://www.linkedin.com/company/mica-hardware?originalSubdomain=za',
    },
    authConfig: {
      issuer: 'https://authdev.loyaltyplus.aero/auth/realms/Mica',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://authdev.loyaltyplus.aero/auth',
      realm: 'Mica',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    useDemoProfile: true, 
    games: {
      globalConfig: {
        version: '1.0.0',
        defaultLanguage: 'en',
        saveUserProgress: true,
        enableSound: true,
        enableMusic: true,
        themes: {
          defaultBackgroundImage: 'assets/images/default-bg.jpg',
        },
      },
      categories: {
        business: {
          name: 'Business Games',
          backgroundImage: 'assets/images/business-bg.jpg',
          games: {
            spinthewheel: {
              name: 'Spin a Wheel',
              backgroundImage: 'assets/images/wheel-bg.jpg',
              config: {
                sections: [
                  {
                    id: 1,
                    label: '10 Points',
                    value: 10,
                    probability: 0.3,
                    backgroundColor: '#FF5733',
                  },
                  {
                    id: 2,
                    label: '20 Points',
                    value: 20,
                    probability: 0.2,
                    backgroundColor: '#33FF57',
                  },
                ],
                spinFrequency: {
                  type: 'daily',
                  resetsAt: '00:00',
                  timezone: 'UTC',
                  spinsAllowed: 3,
                },
                wheelSections: 8,
                spinDuration: 5000,
                minSpinSpeed: 0.1,
                maxSpinSpeed: 2.0,
                prizes: [],
                wheelDesign: {
                  borderColor: '#000000',
                  borderWidth: 2,
                  centerImage: 'assets/images/wheel-center.png',
                  pointerImage: 'assets/images/pointer.png',
                },
              },
            },
            locationBased: {
              name: 'Location Based Photo',
              backgroundImage: 'assets/images/location-bg.jpg',
              config: {
                locations: [
                  {
                    id: 'loc1',
                    name: 'Central Park',
                    latitude: 40.785091,
                    longitude: -73.968285,
                    radius: 100,
                    points: 100,
                    description: 'Take a selfie at Central Park',
                    requiredTags: ['nature', 'park'],
                  },
                ],
                maxPhotoSize: 5242880,
                allowedFileTypes: ['jpg', 'jpeg', 'png'],
                requireLocation: true,
                maxDistanceMeters: 100,
                photoQualityMin: 0.7,
              },
            },
          },
        },
        arcade: {
          name: 'Arcade Games',
          backgroundImage: 'assets/images/arcade-bg.jpg',
          games: {
            memory: {
              name: 'Memory',
              backgroundImage: 'assets/images/memory-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { rows: 2, columns: 2 },
                    timeLimit: 30,
                    points: 100,
                  },
                  {
                    level: 2,
                    gridSize: { rows: 4, columns: 4 },
                    timeLimit: 60,
                    points: 300,
                  },
                  {
                    level: 3,
                    gridSize: { rows: 6, columns: 6 },
                    timeLimit: 120,
                    points: 500,
                  },
                ],
                cardImages: [
                  {
                    id: 1,
                    url: 'assets/images/memory/card1.jpg',
                    name: 'Card 1',
                  },
                  {
                    id: 2,
                    url: 'assets/images/memory/card2.jpg',
                    name: 'Card 2',
                  },
                ],
                cardBackImage: 'assets/images/memory/card-back.jpg',
                matchTime: 1000,
                difficulty: 'medium',
                themeOptions: ['classic', 'animals', 'numbers'],
              },
              faceImages: {
                match: 'assets/images/games/faces/2nd Win Kawaii face.png',
                mismatch: 'assets/images/games/faces/1st loose Kawaii face.png',
                win: 'assets/images/games/faces/3rd Win Kawaii face.png',
                lose: 'assets/images/games/faces/3rd loose Kawaii face.png'
              },
              messages: {
                win: {
                  message: 'Congratulations! You matched all pairs! ',
                  face: 'assets/images/games/faces/3rd Win Kawaii face.png'
                },
                lose: {
                  message: 'Game Over! Better luck next time!',
                  face: 'assets/images/games/faces/3rd loose Kawaii face.png'
                },
                match: {
                  message: 'Great match!',
                  face: 'assets/images/games/faces/2nd Win Kawaii face.png'
                },
                mismatch: {
                  message: 'Try again!',
                  face: 'assets/images/games/faces/1st loose Kawaii face.png'
                }
              }
            },
            wordle: {
              name: 'Wordle',
              backgroundImage: 'assets/images/wordle.png',
              config: {
                wordLength: 5,
                maxAttempts: 6,
                theme: 'light',
              },
              faceImages: {
                correct: 'assets/images/games/faces/2nd Win Kawaii face.png',
                present: 'assets/images/games/faces/3rd loose Kawaii face.png',
                absent: 'assets/images/games/faces/1st loose Kawaii face.png',
                win: 'assets/images/games/faces/3rd Win Kawaii face.png',
                lose: 'assets/images/games/faces/3rd loose Kawaii face.png'
              },
              words: [
                'SMILE', 'BRAVE', 'HAPPY', 'PEACE', 'DREAM',
                'LIGHT', 'DANCE', 'SHINE', 'SPARK', 'HEART',
                'MAGIC', 'POWER', 'STORM', 'OCEAN', 'EARTH',
                'FLAME', 'SWIFT', 'GRACE', 'FAITH', 'BLISS',
                'CHARM', 'GLORY', 'QUEST', 'PRIZE', 'CROWN'
              ],
              win: {
                message: 'Congratulations! You won! ',
                face: 'assets/images/games/faces/3rd Win Kawaii face.png'
              },
              lose: {
                message: 'Game Over! The word was ${this.word}',
                face: 'assets/images/games/faces/3rd loose Kawaii face.png'
              }
            },
            game2048: {
              name: '2048',
              backgroundImage: 'assets/images/2048-bg.jpg',
              config: {
                gridSize: 4,
                winningTile: 2048,
                animationSpeed: 200,
                swipeThreshold: 50,
                colors: {
                  '2': '#EEE4DA',
                  '4': '#EDE0C8',
                  '8': '#F2B179',
                },
                tileDesign: {
                  borderRadius: '3px',
                  fontSize: {
                    small: '24px',
                    medium: '32px',
                    large: '48px',
                  },
                },
                customTileImages: {
                  enabled: false,
                  images: {
                    '2': 'assets/images/2048/tile2.png',
                    '4': 'assets/images/2048/tile4.png',
                  },
                },
              },
            },
            snake: {
              name: 'Snake',
              backgroundImage: 'assets/images/snake-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { width: 10, height: 10 },
                    initialSpeed: 100,
                    speedIncrease: 2,
                    pointsPerFood: 10,
                  },
                  {
                    level: 2,
                    gridSize: { width: 15, height: 15 },
                    initialSpeed: 150,
                    speedIncrease: 3,
                    pointsPerFood: 20,
                  },
                  {
                    level: 3,
                    gridSize: { width: 20, height: 20 },
                    initialSpeed: 200,
                    speedIncrease: 4,
                    pointsPerFood: 30,
                  },
                ],
                foodTypes: [
                  {
                    type: 'regular',
                    points: 1,
                    imageUrl: 'assets/images/snake/apple.png',
                    probability: 0.7,
                  },
                  {
                    type: 'bonus',
                    points: 3,
                    imageUrl: 'assets/images/snake/golden-apple.png',
                    probability: 0.3,
                  },
                ],
                snakeDesign: {
                  headImage: 'assets/images/snake/head.png',
                  bodyImage: 'assets/images/snake/body.png',
                },
              },
            },
            minesweeper: {
              name: 'Minesweeper',
              backgroundImage: 'assets/images/minesweeper-bg.jpg',
              config: {
                difficulties: {
                  beginner: {
                    width: 9,
                    height: 9,
                    mines: 10,
                  },
                  intermediate: {
                    width: 16,
                    height: 16,
                    mines: 40,
                  },
                  expert: {
                    width: 30,
                    height: 16,
                    mines: 99,
                  },
                },
                defaultDifficulty: 'beginner',
                customization: {
                  tileSize: 30,
                  mineImage: 'assets/images/minesweeper/mine.png',
                  flagImage: 'assets/images/minesweeper/flag.png',
                  tileImages: {
                    covered: 'assets/images/minesweeper/covered.png',
                    uncovered: 'assets/images/minesweeper/uncovered.png',
                  },
                },
                animations: {
                  reveal: true,
                  explosion: true,
                },
              },
            },
            sudoku: {
              name: 'Sudoku',
              backgroundImage: 'assets/images/sudoku-bg.jpg',
              config: {
                difficulties: ['easy', 'medium', 'hard', 'expert'],
                defaultDifficulty: 'medium',
                highlightSameNumbers: true,
                showMistakes: true,
                hints: {
                  maximum: 3,
                  penaltyMinutes: 5,
                },
                design: {
                  gridLineWidth: {
                    normal: 1,
                    bold: 2,
                  },
                  colors: {
                    highlight: '#f0f0f0',
                    error: '#ffdddd',
                    success: '#ddffdd',
                  },
                  fonts: {
                    numbers: 'Arial',
                    notes: 'Arial',
                  },
                },
                timer: {
                  enabled: true,
                  format: 'mm:ss',
                },
              },
            },
            tetris: {
              name: 'Tetris',
              backgroundImage: 'assets/images/games/tetris.jpeg',
              config: {
                startLevel: 1,
                maxLevel: 20,
                boardSize: {
                  width: 10,
                  height: 20,
                },
                ghostPiece: true,
                holdPiece: true,
                showNext: 3,
                scoringSystem: {
                  singleLine: 100,
                  doubleLine: 300,
                  tripleLine: 500,
                  tetris: 800,
                },
                design: {
                  blockSize: 30,
                  colors: {
                    I: '#00f0f0',
                    O: '#f0f000',
                    T: '#a000f0',
                    S: '#00f000',
                    Z: '#f00000',
                    J: '#0000f0',
                    L: '#f0a000',
                  },
                  customBlocks: {
                    enabled: false,
                    blockImages: {
                      I: 'assets/images/tetris/i-block.png',
                      O: 'assets/images/tetris/o-block.png',
                    },
                  },
                },
                particles: {
                  enabled: true,
                  lineComplete: true,
                },
              },
            },
          },
        },
      },
    },
  },
};
