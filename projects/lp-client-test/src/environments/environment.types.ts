import { PageConfiguration, NavigationConfiguration } from './page-configuration.types';

export interface LssConfig {
  googleApiKey?: string;
  apiId?: string;
  appCode: string;
  appName: string;
  appVersion?: string;
  useAuth: boolean;
  useION: boolean;
  useISO: boolean;
  defaultNotAuthURL: string;
  autoLogout: boolean;
  autoLogoutTimeout: number;
  autoLogoutWarning: number;
  defaultLat?: number;
  defaultLng?: number;
  loadIdentity: boolean;
  identityBaseUrl: string;
  appBaseUrl: string;
  apiBaseUrl: string;
  configAPIUrl?: string;
  memberPhone?: {
    dialCode: string;
    nationalNumber: string;
  };
  memberCard?: string;
  telephone?: {
    selectFirstCountry: boolean;
    preferredCountries: string[];
    onlyCountries: string[];
  };
  theme?: {
    layout: string;
    backgroundImage: string;
    colours: {
      primary: string;
      primaryContrast: string;
      primaryShade: string;
      primaryTint: string;
      secondary: string;
      secondaryContrast: string;
      secondaryShade: string;
      secondaryTint: string;
    };
  };
  games?: {
    [key: string]: any;
  };
  authConfig?: {
    [key: string]: any;
  };
  // Dynamic pages configuration (standardized array format)
  pages?: PageConfiguration[];
  // Navigation configuration
  navigation?: NavigationConfiguration;
  // Additional configuration options
  useDemoProfile?: boolean;
  // Contact information
  contact?: {
    callCenter?: string;
    email?: string;
    website?: string;
  };
  // Social media links
  socials?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
    pinterest?: string;
  };
  // Points configuration
  pointsExpire?: boolean;
  pointsTitle?: string;
  // Terms and conditions
  termsConditions?: string;
  // Application icon
  icon?: string;
  // Log endpoint
  logEndpoint?: string;
  // Workflow Configuration
  workflow?: {
    type?: string;
    multiTenant?: boolean;
    productId?: string; // Program/product identifier for single-tenant apps
    programSelection?: any;
    features?: any;
    navigation?: {
      programDependentRoutes?: string[];
      alwaysVisibleRoutes?: string[];
    };
  };
}

export interface Environment {
  production: boolean;
  env: string;
  client?: string;
  lssConfig: LssConfig;
}
