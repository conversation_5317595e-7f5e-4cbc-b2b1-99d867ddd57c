// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'DEV',
  client: 'rmic',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'Make It With Mica',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://rmicdev.loyaltyplus.aero/',
    logEndpoint: 'https://rmicdev.loyaltyplus.aero/extsecure/tools/logservice',
    // Disable remote config fetch in dev to avoid wiping out apiBaseUrl
    configAPIUrl: '',
    termsConditions:
      'https://rmicdev.loyaltyplus.aero/rmic/rmic/terms/Mica_Terms_Conditions.pdf',
    pointsExpire: false,
    pointsTitle: 'Points',
    memberPhone: {
      dialCode: '+27',
      nationalNumber: '',
    },
    memberCard: '',

    telephone: {
      selectFirstCountry: true,
      preferredCountries: ['za'],
      onlyCountries: ['za', 'ls', 'bw', 'na', 'sz', 'mz'],
    },
    theme: {
      layout: 'sidebar',
      backgroundImage: '',
      colours: {
        primary: '#f5821f', // MICA orange
        primaryContrast: '#ffffff',
        primaryShade: '#dc6a0a',
        primaryTint: '#ff9435',

        secondary: '#ffd400',
        secondaryContrast: '#ffffff',
        secondaryShade: '#4854e0',
        secondaryTint: '#6370ff',

        tertiary: '#ffd400',
        tertiaryContrast: '#ffffff',
        tertiaryShade: '#4854e0',
        tertiaryTint: '#6370ff',

        success: '#2dd36f',
        successContrast: '#000000',
        successShade: '#28ba62',
        successTint: '#42d77d',

        warning: '#ffc409',
        warningContrast: '#000000',
        warningShade: '#e0ac08',
        warningTint: '#ffca22',

        danger: '#eb445a',
        dangerContrast: '#ffffff',
        dangerShade: '#cf3c4f',
        dangerTint: '#ed576b',

        medium: '#92949c',
        mediumContrast: '#000000',
        mediumShade: '#808289',
        mediumTint: '#9d9fa6',

        base: '#0b69b4',
        baseContrast: '#ffffff',
        baseShade: '#16315d',
        baseTint: '#4c8dff',

        light: '#f4f5f8',
        lightContrast: '#000000',
        lightShade: '#d7d8da',
        lightTint: '#f5f6f9',

        //--ion-background-color: '#f6f6f6',
        //--ion-background-color: transparent',

        // --ion-text-color: '#737373',
        // --ion-text-color: '#fff',

        // --ion-item-color: '#737373',

        step50: '#f8f8f8',
        step100: '#f1f1f1',
        step150: '#eaeaea',
        step200: '#e3e3e3',
        step250: '#dcdcdc',
        step300: '#d5d5d5',
        step350: '#cecece',
        step400: '#c7c7c7',
        step450: '#c0c0c0',
        step500: '#b9b9b9',
        step550: '#b2b2b2',
        step600: '#ababab',
        step650: '#a4a4a4',
        step700: '#9d9d9d',
        step750: '#969696',
        step800: '#8f8f8f',
        step850: '#888888',
        step900: '#818181',
        step950: '#7a7a7a',
      },
    },
    forms: {
      login: {
        options: {
          email: true,
          google: true,
        },
      },
      contactus: {
        categories: ['General', 'Complaint'],
      },
    },
    pages: {
      landing: {
        autoRedirectOnLogin: true, // Enable auto-redirect to secure dashboard after login
        loggedinIcon: 'assets/images/make-it.png'
      }
    },
    // Copy lp-client page config structure so lp-client-test behaves the same
    pageConfigs: [
      // PUBLIC PAGES (secure: false)
      // Landing and Home Pages
      {
        title: 'landing',
        path: 'landing',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              lssConfig: 'lssConfig',
              title: {
                text: 'Welcome',
                class: 'text-primaryContrast text-4xl text-center',
              },
              subtitle: {
                text: 'to Make It With Mica',
                class: 'text-primaryContrast text-center w-full',
              },
              icon: {
                src: 'assets/images/make-it.png',
                class: 'rounded-large text-center w-80 mx-auto',
              }
            }
          },
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig'
            }
          }
        ]
      },
      {
        title: 'home',
        path: 'home',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              lssConfig: 'lssConfig'
            }
          },
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig'
            }
          }
        ]
      },

      // Authentication Pages
      {
        title: 'login',
        path: 'login',
        secure: false,
        class: 'bg-base h-screen',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            inputs: {
              kc: 'kc',
              lssConfig: 'lssConfig',
              title: {
                text: 'Welcome',
                class: 'text-primaryContrast text-4xl text-center',
              },
              subtitle: {
                text: 'to Make It With Mica',
                class: 'text-primaryContrast text-center w-full',
              },
              icon: {
                src: 'assets/images/make-it.png',
                class: 'rounded-large text-center w-80 mx-auto',
              }
            }
          }
        ]
      },
      {
        title: 'signup',
        path: 'signup',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Create Account',
              subtitle: 'Join the Make It With Mica loyalty program'
            }
          },
          {
            type: 'SignupComponent',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router'
            }
          }
        ]
      },
      {
        title: 'otp',
        path: 'otp',
        secure: false,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'OTP Verification',
              subtitle: 'Enter the code sent to your device'
            }
          },
          {
            type: 'OtpValidatorComponent',
            inputs: {
              length: 6,
              autoSubmit: true
            }
          }
        ]
      },
      {
        title: 'validate',
        path: 'validate',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Card Validation',
              subtitle: 'Validate your loyalty card'
            }
          },
          {
            type: 'ValidateComponent',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router'
            }
          }
        ]
      },
      {
        title: 'password',
        path: 'password',
        secure: false,
        class: 'bg-base h-full p-4',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Reset Password',
              subtitle: 'Enter your email to reset password'
            }
          },
          {
            type: 'InputComponent',
            inputs: {
              type: 'email',
              placeholder: 'Enter your email',
              label: 'Email Address'
            }
          },
          {
            type: 'ButtonComponent',
            inputs: {
              text: 'Send Reset Link',
              expand: 'block',
              fill: 'solid',
              color: 'primary'
            }
          }
        ]
      },

      // SECURE PAGES (secure: true)
      // Account Management Pages
      {
        title: 'profile',
        path: 'profile',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'ProfileHeaderComponent',
            inputs: {
              profile: 'profile',
              title: 'My Profile'
            }
          },
          {
            type: 'ProfileFormComponent',
            inputs: {
              profile: 'profile',
              memberService: 'memberService'
            }
          },
          {
            type: 'ProfileSettingsComponent',
            inputs: {
              profile: 'profile'
            }
          },
          {
            type: 'ProfileHelpComponent',
            inputs: {
              contactInfo: 'lssConfig.contact'
            }
          }
        ]
      },
      {
        title: 'profile-details',
        path: 'profile-details',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Profile Details',
              subtitle: 'Update your information'
            }
          },
          {
            type: 'InputComponent',
            inputs: {
              type: 'text',
              placeholder: 'First Name',
              label: 'First Name'
            }
          },
          {
            type: 'InputComponent',
            inputs: {
              type: 'text',
              placeholder: 'Last Name',
              label: 'Last Name'
            }
          },
          {
            type: 'ButtonComponent',
            inputs: {
              text: 'Save Changes',
              expand: 'block',
              fill: 'solid',
              color: 'primary'
            }
          }
        ]
      },
      {
        title: 'security',
        path: 'security',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Security Settings',
              subtitle: 'Manage your account security'
            }
          },
          {
            type: 'ListComponent',
            inputs: {
              items: [
                {
                  title: 'Change Password',
                  subtitle: 'Update your password',
                  icon: 'lock-closed-outline'
                },
                {
                  title: 'Two-Factor Authentication',
                  subtitle: 'Enable 2FA for extra security',
                  icon: 'shield-checkmark-outline'
                }
              ]
            }
          }
        ]
      },

      // Dashboard (Secure Home)
      {
        title: 'dashboard',
        path: 'dashboard',
        secure: true,
        class: 'bg-base h-full',
        components: [
                    {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig'
            },
                        },
          
        ]
      },

      // Financial Pages
      {
        title: 'transactions',
        path: 'transactions',
        secure: true,
        class: 'min-h-screen',
        components: [
                     {
            type: 'HeadLogoComponent',
            inputs: {
              names: '$profile.givenNames + " " + $profile.surname',
              membership: '$profile.externalId',
              type: 'card', 
              balance: '$profile.currentBalance',
              src: 'assets/images/make-it.png'
            }
          },
            {
     type: 'TransactionsPageComponent',
     inputs: {
       profile: 'profile',
       lssConfig: 'lssConfig',
       apiBaseUrl: 'lssConfig.apiBaseUrl',
       productId: 'lssConfig.workflow?.productId'
     }
   }
        ]
      },
      {
        title: 'virtualcard',
        path: 'virtualcard',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              names: '$profile.givenNames + " " + $profile.surname',
              membership: '$profile.externalId',
              type: 'card', 
              balance: '$profile.currentBalance',
              src: 'assets/images/make-it.png'
            }
          },
          {
         type: 'VirtualCardPageComponent',
         inputs: {
           profile: 'profile',
           memberService: 'memberService',
           keyCloakService: 'keyCloakService',
           lssConfig: 'lssConfig',
           multiTenantContext: 'multiTenantContext',
           apiBaseUrl: 'lssConfig.apiBaseUrl',
           productId: 'lssConfig.workflow?.productId'
         }
       }
        ]
      },
         {
     title: 'statements',
     path: 'statements',
     secure: true,
     class: 'bg-base h-full',
     components: [
{
            type: 'HeadLogoComponent',
            inputs: {
              names: '$profile.givenNames + " " + $profile.surname',
              membership: '$profile.externalId',
              type: 'card', 
              balance: '$profile.currentBalance',
              src: 'assets/images/make-it.png'
            }
          },
       {
         type: 'StatementsPageComponent',
         inputs: {
           profile: 'profile',
           memberService: 'memberService',
           lssConfig: 'lssConfig',
           apiBaseUrl: 'lssConfig.apiBaseUrl'
         }
       }
     ]
   },

      // Support Pages
      {
        title: 'contactus',
        path: 'contactus',
        secure: true,
        class: 'bg-base h-full',
        components: [
           {
            type: 'HeadLogoComponent',
            inputs: {
              names: '$profile.givenNames + " " + $profile.surname',
              membership: '$profile.externalId',
              type: 'card', 
              balance: '$profile.currentBalance',
              src: 'assets/images/make-it.png'
            }
          },
      {
        type: 'ContactusComponent',
        inputs: {
          categoryCode: 'CNCT',
          categories: ['General', 'Complaint']
        }
      }
        ]
      },

      // Settings Pages
     {
  title: 'settings',
  path: 'settings',
  secure: true,
  class: 'bg-base h-full',
  components: [
    {
      type: 'SettingsPageComponent',
      inputs: {
        profile: 'profile',
                            showUserProfile: 'true',
   
      
      }
    }
  ]
},
      {
        title: 'notification-settings',
        path: 'notification-settings',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'NotificationSettingsPageComponent',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig',
              pushNotificationService: 'pushNotificationService',
              memberService: 'memberService',
              firebaseMemberService: 'firebaseMemberService'
            }
          }
        ]
      },
      // Store and Shopping Pages
      {
        title: 'stores',
        path: 'stores',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'StoresPageComponent',
            inputs: {
              profile: 'profile',
              memberService: 'memberService',
              partnerService: 'partnerService',
              systemService: 'systemService',
              keyCloakService: 'kc',
              lssConfig: 'lssConfig'
            },
            outputs: {
              storeSelected: 'handleStoreNavigation'
            }
          }
        ]
      },
      {
        title: 'products',
        path: 'products',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Products',
              subtitle: 'Browse available products'
            }
          },
          {
            type: 'ProductsPageComponent',
            inputs: {
              profile: 'profile',
              lssConfig: 'lssConfig'
            }
          }
        ]
      },
      // Virtual Card public version
      {
        title: 'virtual',
        path: 'virtual',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Virtual Card',
              subtitle: 'Your Make It With Mica card preview'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Please log in to view your virtual card.',
              type: 'info'
            }
          }
        ]
      },
      // Account/Profile public version
      {
        title: 'account',
        path: 'account',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Account',
              subtitle: 'Manage your profile'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Please log in to view your account details.',
              type: 'info'
            }
          }
        ]
      },
      // Pools/Communities
      {
        title: 'pools',
        path: 'pools',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'Communities',
              subtitle: 'Join and manage your communities'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Community features coming soon.',
              type: 'info'
            }
          }
        ]
      },
      // Program Management
      {
        title: 'program-management',
        path: 'program-management',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            inputs: {
              logo: 'assets/images/make-it.png',
              title: 'My Programs',
              subtitle: 'Manage your loyalty programs'
            }
          },
          {
            type: 'MessageComponent',
            inputs: {
              text: 'Program management features coming soon.',
              type: 'info'
            }
          }
        ]
      }
    ],
    contact: {
      callCenter: '012 141 3596',
    },
    socials: {
      facebook: 'https://www.facebook.com/micahardware',
      twitter: 'https://twitter.com/micahardware',
      linkedin:
        'https://www.linkedin.com/company/mica-hardware?originalSubdomain=za',
      youtube: 'https://www.youtube.com/channel/UCrCGdzTbu8xWKpubdHz9VjA',
      pinterest: 'https://za.pinterest.com/micahardware/',
      instagram: 'https://www.instagram.com/micahardware/?hl=en',
    },
    terminology: {
      pool: {
        singular: 'Community',
        plural: 'Communities',
        management: 'Community Management',
        member: 'Community Member',
        notInMessage: 'Member is currently not in a Community',
        createNew: 'Create New Community',
        joinExisting: 'Join Existing Community',
        invitation: 'Community Invitation',
        exitConfirmTitle: 'Exit Community',
        exitConfirmMessage: 'Are you sure you want to exit the community',
        exitWarning:
          'Warning: This action cannot be undone. You will need to be re-invited to rejoin the community.',
        createdSuccess: 'Community created successfully!',
        joinRequestSuccess: 'Join request sent successfully!',
        invitationSentSuccess: 'Invitation sent successfully!',
        exitSuccess: 'You have successfully exited the community.',
        removeMemberSuccess: 'Member removed successfully!',
        approveJoinSuccess:
          'Join request approved! Member has been added to the community.',
        rejectJoinSuccess: 'Join request rejected and member removed.',
        acceptInviteSuccess: 'Successfully joined the community!',
        inviteAcceptedToast: 'Community invitation accepted successfully!',
        inviteDeclinedToast: 'Community invitation declined',
        errorOccurred: 'An error occurred with the community',
        exitToast: 'You have successfully exited the community',
        inviteToast:
          'Successfully invited member {membershipNumber} to the community!',
        loadingInfo: 'Loading community information...',
        noInfoAvailable: 'No community information available.',
        alreadyInAnother:
          'This member is already in another community and cannot join this one.',
        failedToCreate: 'Failed to create community. Please try again.',
        failedToJoin: 'Failed to request community join. Please try again.',
        failedToExit: 'Failed to exit community. Please try again.',
        failedToLoad: 'Failed to load community information',
        acceptInfoText:
          'By accepting this invitation, you will become a member of this community and can participate in communities activities.',
        totalUnits: 'Total Points',
      },
    },
    navigation: {
      sidebarTitle: 'Mica',
      sidebarIcon: 'assets/images/make-it.png',
      type: 'hybrid', // Options: 'sidebar', 'bottomNav', 'hybrid'
      layout: {
        desktop: {
          sidebar: {
            enabled: true,
            width: '280px',
            showProfile: true,
            collapsible: false
          },
          header: {
            enabled: true,
            showBack: true,
            showNotifications: true,
            showMobileMenuToggle: false
          }
        },
        mobile: {
          bottomNav: {
            enabled: true,
            maxItems: 4, // Items before "More" menu
            mobileOnly: true
          },
          overlay: {
            enabled: true,
            showProfile: true,
            backdrop: true
          },
          header: {
            enabled: true,
            showBack: true,
            showNotifications: true,
            showMobileMenuToggle: true
          }
        },
        breakpoints: {
          mobile: 768,
          tablet: 1024
        }
      },
      routes: [
        {
          id: 'Login',
          text: 'Login',
          link: 'login',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Register',
          text: 'Register',
          link: 'register',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Logout',
          text: 'Logout',
          link: 'logout',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Home',
          text: 'Home',
          path: '/public/home',
          link: 'home',
          icon: 'home-outline',
          active: true,
          sidebar: true,
          main: true,
          more: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Profile',
          text: 'Profile',
          path: '/public/profile',
          link: 'profile',
          icon: 'person-outline',
          active: true,
          sidebar: true,
          main: true,
          more: false,
          admin: false,
          sort: 1,
        },
        {
          id: 'Card',
          text: 'Virtual Card',
          path: '/secure/virtualcard',
          link: 'virtualcard',
          icon: 'card-outline',
          active: true,
          sidebar: true,
          main: false,
          more: true,
          admin: false,
          sort: 2,
        },
        {
          id: 'Transactions',
          text: 'Transactions',
          path: '/secure/transactions',
          link: 'transactions',
          icon: 'receipt-outline',
          active: true,
          sidebar: true,
          main: true,
          more: false,
          admin: false,
          sort: 3,
        },
        {
          id: 'Dashboard',
          text: 'Dashboard',
          path: '/secure/dashboard',
          link: 'dashboard',
          icon: 'apps-outline',
          active: true,
          sidebar: true,
          main: true,
          more: false,
          admin: false,
          sort: 4,
        },
        {
          id: 'Stores',
          text: 'Stores',
          path: '/public/stores',
          link: 'stores',
          icon: 'location-outline',
          active: true,
          sidebar: true,
          main: false,
          more: true,
          admin: false,
          sort: 5,
        },
        {
          id: 'Products',
          text: 'Products',
          path: '/public/products',
          link: 'products',
          icon: 'gift-outline',
          active: true,
          sidebar: true,
          main: false,
          more: true,
          admin: false,
          sort: 6,
        },
        {
          id: 'Contact',
          text: 'Contact Us',
          path: '/public/contactus',
          link: 'contactus',
          icon: 'chatbubbles-outline',
          active: true,
          sidebar: true,
          main: false,
          more: true,
          admin: false,
          sort: 7,
        },
        {
          id: 'Settings',
          text: 'Settings',
          path: '/secure/settings',
          link: 'settings',
          icon: 'settings-outline',
          active: true,
          sidebar: true,
          main: false,
          more: true,
          admin: false,
          sort: 8,
        },
        {
          id: 'Statements',
          text: 'Statements',
          path: '/secure/statements',
          link: 'statements',
          icon: 'document-outline',
          active: true,
          sidebar: true,
          main: false,
          more: true,
          admin: false,
          sort: 9,
        },
        {
          id: 'NotificationSettings',
          text: 'Notifications',
          path: '/secure/notification-settings',
          link: 'notification-settings',
          icon: 'notifications-outline',
          active: true,
          sidebar: true,
          main: false,
          more: true,
          admin: false,
          sort: 10,
        },
      ],
      bottomNav: {
        enabled: false,
        mobileOnly: true
      }
    },
    authConfig: {
      issuer: 'https://authdev.loyaltyplus.aero/auth/realms/Mica',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://authdev.loyaltyplus.aero/auth',
      realm: 'Mica',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        redirectUri: window.location.origin.replace('.aero', '.aero/lp-mobile'),
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    // Workflow Configuration
    workflow: {
      // Set to 'simple' for direct login → home
      // Set to 'program-selection' for login → program selection → onboarding → home
      type: 'simple', // Options: 'simple' | 'program-selection'
      
      // Product/Program identifier for single-tenant apps
      productId: 'rmic', // As per Emil's conversation
      
      // Multi-tenant configuration (not used in single-tenant mode)
      multiTenant: false,
      
      // Workflow 2: Program Selection Configuration
      programSelection: {
        enabled: true,
        requiredPrograms: ['loyalty-base'], // Programs that must be selected
        skipOptional: true, // Allow skipping optional programs
        categories: ['rewards', 'benefits', 'partnerships'],
        onboardingSteps: ['programs', 'profile', 'preferences', 'welcome'],
        mockData: true, // Use mock data for development
      },
      
      // Feature flags for gradual rollout
      features: {
        programSelection: true,
        onboardingFlow: true,
        welcomeDashboard: true,
        skipOnboarding: false, // Set to true to bypass for existing users
        pools: {
          extsecure: {
            readEnabled: true,
            mutateEnabled: true
          }
        }
      },
      
      // Navigation configuration (empty for single-tenant)
      navigation: {
        programDependentRoutes: [] as string[],
        alwaysVisibleRoutes: [] as string[]
      },
    },
  },
};
