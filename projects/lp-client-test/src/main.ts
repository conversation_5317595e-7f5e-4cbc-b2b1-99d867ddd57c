import { enableProdMode, import<PERSON>rovidersFrom, APP_INITIALIZER } from '@angular/core';
import { bootstrapApplication } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
// import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { AppComponent } from './app/app.component';
import { BrowserModule, HammerModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app/app-routing.module';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { RouteReuseStrategy } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GoogleMapsModule } from '@angular/google-maps';
import { InterceptorService, KeyCloakService, LogPublishersService, LogService, LssConfig } from 'lp-client-api';
import { ConfigService } from './app/services/config.service';
import { BuilderBridgeReceiverService } from './app/services/builder-bridge-receiver.service';
import { BridgeApiService } from './app/services/bridge-api.service';
import { environment } from './environments/environment';

if (environment.production) {
  enableProdMode();
}

// platformBrowserDynamic().bootstrapModule(AppModule)
//   .catch(err => console.error(err));

function initializeKeycloak(config: ConfigService) {
  return () => config.loadConfig();
}

bootstrapApplication(AppComponent, {
  providers: [
    provideAnimations(), // Add animations support
    importProvidersFrom(
      BrowserModule,
      FontAwesomeModule,
      HttpClientModule,
      IonicModule.forRoot(),
      AppRoutingModule,
      HammerModule,
      GoogleMapsModule,
      AppModule
    ),
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    ConfigService,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [ConfigService],
    },
    KeyCloakService,
    LogService,
    LssConfig,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true,
    },
    LogPublishersService,
    BuilderBridgeReceiverService,
    BridgeApiService,
    { provide: 'componentProperties', useValue: {} },
    { provide: 'environment', useValue: environment }
  ]
}).catch(err => console.error(err));
