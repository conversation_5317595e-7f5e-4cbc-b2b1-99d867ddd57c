import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { Router, ActivationStart, RouterOutlet, RouterModule } from '@angular/router';
import { Platform, IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

import { LocationStrategy } from '@angular/common';

import { LoadingController, MenuController } from '@ionic/angular';
import { ComponentRegistryService } from './shared/component-registry.service';
import {
  AbstractService,
  KeyCloakService,
  LogService,
  LssConfig,
  MemberProfile,
  MemberService,
} from 'lp-client-api';
import { AuthFlowService } from './core/auth/auth-flow.service';
import { App, URLOpenListenerEvent } from '@capacitor/app';
import { environment } from '../environments/environment';
import { Preferences } from '@capacitor/preferences';

interface NavItem {
  path: string;
  icon: string;
  label: string;
  exact: boolean;
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, RouterModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild(RouterOutlet)
  outlet: RouterOutlet = new RouterOutlet();

  private routerSubscription: Subscription = new Subscription();

  // Navigation arrays for different nav types
  navItems: NavItem[] = [];
  mainNavItems: NavItem[] = [];
  moreNavItems: NavItem[] = [];
  sidebarMenuItems: NavItem[] = [];
  
  // App state
  count?: any = 0;
  loading?: HTMLIonLoadingElement;
  slowLoad = false;
  environment = environment;
  profile?: MemberProfile;
  appVersion = '1.0.0';
  
  // Menu state
  _currentMenu = { icon: '', label: '', path: '' };
  isMoreMenuOpen = false;
  mobileMenuOpen = false;
  
  // Responsive navigation state
  isMobile = false;
  isTablet = false;
  private resizeListener: any;

  constructor(
    private menu: MenuController,
    private kc: KeyCloakService,
    protected readonly router: Router,
    private memberService: MemberService,
    private logService: LogService,
    private platform: Platform,
    private loadingCtrl: LoadingController,
    public lssConfig: LssConfig,
    private LocationStrategy: LocationStrategy,
    // Force early instantiation of the dynamic Component Registry
    private readonly registry: ComponentRegistryService,
    private readonly authFlowService: AuthFlowService
  ) {
    console.log('[AppComponent] Constructor called');
    console.log('[AppComponent] LssConfig:', this.lssConfig);
    this.platform.ready().then(() => {
      if (this.platform.is('hybrid')) {
        App.addListener('appStateChange', ({ isActive }) => {
          console.log('App state changed. Is active?', isActive);
          if (isActive && this.kc.authSuccess) {
            if (this.kc.keycloak?.tokenParsed) {
              if (this.kc.keycloak.tokenParsed.exp) {
                if (
                  this.kc.keycloak.tokenParsed.exp <
                  new Date().getTime() / 1000
                ) {
                  this.kc.authed().then((authed: boolean) => {
                    if (!authed) {
                      console.log('Go to login screen');
                    }
                  });
                }
              }
            }
          }
        });
        App.addListener('appUrlOpen', (data) => {
          console.log('App opened with URL:', data);
        });
      }
    });
  }

  ngOnInit() {
    console.log('[AppComponent] ngOnInit called');
    
    // Initialize responsive behavior
    this.setupResponsiveNavigation();
    
    // Debug dump: ensure components are registered before any dynamic pages validate
    setTimeout(() => this.registry.logRegisteredComponents(), 500);
    this.deactivateOutletOnRouteChange();
    this.generateMenu();
    
    // Subscribe to profile changes for display in navigation
    this.memberService.profileSubject?.subscribe((profile) => {
      this.profile = profile;
    });
    
    this.kc.authStatus.subscribe((data) => {
      console.log('this.kc.authStatus', data);
      if (data != null && data.eventName === 'init') {
        this.loadingCtrl
          .create({
            message: 'Loading Authentication',
            duration: 10000,
          })
          .then((data) => {
            console.log('Loading created');
            if (!this.slowLoad) {
              this.loading = data;
              this.loading.present();
            } else {
              this.slowLoad = false;
            }
          });
      } else {
        this.slowLoad = true;
        if (this.loading) {
          this.loading.dismiss();
        }
      }
      if (data != null) {
        if (data.eventName === 'login') {
          console.log('[AppComponent] Login event detected, delegating to AuthFlowService');
          
          // Use AuthFlowService to handle the entire post-login flow
          this.authFlowService.handleAuthLogin().subscribe({
            next: () => {
              console.log('[AppComponent] Auth flow completed successfully');
              // Get notification count after successful login
              this.authFlowService.getNotificationCount();
              // Regenerate menu in case auth state affects menu items
              this.generateMenu();
            },
            error: (error) => {
              console.error('[AppComponent] Error in auth flow:', error);
              // Still regenerate menu even if there was an error
              this.generateMenu();
            }
          });
        } else if (data.eventName === 'logout') {
          Preferences.remove({ key: 'login' }).then(() => {
            console.log('Token Logout');
          });
        }
      }
    });
    this.generateMenu();
  }

  deactivateOutletOnRouteChange(): void {
    this.routerSubscription = this.router.events.subscribe((e) => {
      if (e instanceof ActivationStart && e.snapshot.outlet === 'primary') {
        if (this.outlet && this.outlet.isActivated) {
          this.outlet.deactivate();
        }
      }
    });
  }

  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
    }
    this.kc.authStatus.unsubscribe();
  }

  generateMenu(): void {
    console.log('[AppComponent] Generating navigation menus');
    
    // Use the navigation data directly from the environment object
    const nav = this.environment.lssConfig?.navigation || this.lssConfig?.navigation;
    const routes = nav?.routes || [];
    
    console.log('[AppComponent] Available routes:', routes.length);
    
    // Filter routes based on authentication state and active status
    const activeRoutes = routes.filter((route: any) => route.active);
    
    // Generate different navigation arrays for different layouts
    this.sidebarMenuItems = activeRoutes
      .filter((rt: any) => rt.sidebar)
      .sort((a: any, b: any) => (a.sort || 0) - (b.sort || 0))
      .map((route: any) => this.mapRouteToNavItem(route));
    
    this.mainNavItems = activeRoutes
      .filter((rt: any) => rt.main)
      .sort((a: any, b: any) => (a.sort || 0) - (b.sort || 0))
      .map((route: any) => this.mapRouteToNavItem(route))
      .slice(0, 4); // Limit to 4 items for bottom nav
    
    this.moreNavItems = activeRoutes
      .filter((rt: any) => rt.more || (rt.main && this.mainNavItems.length >= 4))
      .sort((a: any, b: any) => (a.sort || 0) - (b.sort || 0))
      .map((route: any) => this.mapRouteToNavItem(route));
    
    // General nav items for mobile overlay (combines sidebar items)
    this.navItems = this.sidebarMenuItems;
    
    console.log('[AppComponent] Generated navigation:', {
      sidebar: this.sidebarMenuItems.length,
      main: this.mainNavItems.length, 
      more: this.moreNavItems.length,
      mobile: this.navItems.length
    });
  }
  
  private mapRouteToNavItem(route: any): NavItem {
    // Use the full path if available, otherwise construct it from the link
    let fullPath = route.path || '/';
    
    // If we only have a link (page ID), we need to construct the full path
    if (!route.path && route.link) {
      // For navigation, we should use the configured path or construct one
      fullPath = route.path || `/${route.link}`;
      
      // Check if this is a public or secure page by looking at the page configuration
      const pageConfigs = this.environment.lssConfig?.pageConfigs || [];
      const pageConfig = pageConfigs.find((p: any) => p.path === route.link);
      
      if (pageConfig) {
        if (pageConfig.secure) {
          fullPath = `/secure/${route.link}`;
        } else {
          fullPath = `/public/${route.link}`;
        }
      } else {
        // Default to public if no page config found
        fullPath = `/public/${route.link}`;
      }
    }
    
    return {
      path: fullPath,
      icon: route.icon || 'ellipse-outline',
      label: route.text || route.label || 'Unknown',
      exact: route.exact || route.link === '/' || route.link === 'home' || route.link === 'landing'
    };
  }

  openMenu() {
    this.menu.open('end');
  }

  get loggedin(): boolean | undefined {
    return this.kc.authSuccess;
  }

  get pageTitle(): string {
    let url = this.router.url;
    if (url === '/') {
      url = 'pages/home';
    } else {
      url = url.substring(1);
    }
    if (this._currentMenu == null || url !== this._currentMenu.path) {
      this._currentMenu = this.navItems.filter(
        (filter) => url === filter.path
      )[0];
    }
    return url;
  }


  close(menuItem: any) {
    this.menu.close();
    this._currentMenu = menuItem;
  }
  back(): void {
    if (this.LocationStrategy.historyGo) this.LocationStrategy.historyGo(-1);
  }
  login() {
    if (!this.kc.authSuccess) {
      this.kc.keycloak?.login().then((data) => {
        console.log(data);
      });
    }
  }

  logout() {
    if (this.kc.authSuccess) {
      this.kc.keycloak?.logout();
    }
  }

  getNotificationCount() {
    // Use .then() to check the resolved value of the promise
    this.kc.authed().then(isAuthed => {
      if (isAuthed) {
        const membershipId = this.kc.lpUniueReference;
        if (membershipId && membershipId !== 'Not Set') {
          this.memberService
            .getNotificationsCount(membershipId)
            .subscribe({
            error: (error) => {
              console.log(error.message);
            },
            next: (body: any) => {
              console.log('body', body.count);
              if (body !== undefined) {
                this.count = body.count;
              }
            },
          });
        }
      }
    });
  }

  get showMenuBack(): boolean | undefined {
    return this.router.url.includes('/public/landing') ? false : true;
  }

  toggleMoreMenu(): void {
    this.isMoreMenuOpen = !this.isMoreMenuOpen;
  }

  // Navigation animation methods
  addClickAnimation(event: Event): void {
    const target = event.currentTarget as HTMLElement;
    target.classList.add('clicked');
    setTimeout(() => {
      target.classList.remove('clicked');
    }, 200);
  }

  updateIndicator(index: number): void {
    const indicator = document.querySelector('.nav-indicator') as HTMLElement;
    if (indicator) {
      const navItems = document.querySelectorAll('.nav-item');
      const activeItem = navItems[index] as HTMLElement;
      if (activeItem) {
        const itemRect = activeItem.getBoundingClientRect();
        const navRect = activeItem.parentElement?.getBoundingClientRect();
        if (navRect) {
          const left = itemRect.left - navRect.left + (itemRect.width / 2) - 20;
          indicator.style.transform = `translateX(${left}px)`;
        }
      }
    }
  }
  
  // ===========================================
  // RESPONSIVE NAVIGATION METHODS
  // ===========================================
  
  private setupResponsiveNavigation(): void {
    this.checkScreenSize();
    this.resizeListener = this.checkScreenSize.bind(this);
    window.addEventListener('resize', this.resizeListener);
  }
  
  private checkScreenSize(): void {
    const width = window.innerWidth;
    const breakpoints = (this.environment.lssConfig?.navigation as any)?.layout?.breakpoints || {
      mobile: 768,
      tablet: 1024
    };
    
    this.isMobile = width <= breakpoints.mobile;
    this.isTablet = width > breakpoints.mobile && width <= breakpoints.tablet;
    
    console.log('[AppComponent] Screen size check:', {
      width,
      isMobile: this.isMobile,
      isTablet: this.isTablet
    });
  }
  
  // ===========================================
  // NAVIGATION DISPLAY LOGIC
  // ===========================================
  
  get showDesktopSidebar(): boolean {
    const navConfig = this.environment.lssConfig?.navigation as any;
    const layout = navConfig?.layout?.desktop;
    
    // Show sidebar on desktop when enabled and user is logged in
    return !this.isMobile && 
           layout?.sidebar?.enabled && 
           this.loggedin &&
           (navConfig?.type === 'sidebar' || navConfig?.type === 'hybrid');
  }
  
  get showHeader(): boolean {
    const navConfig = this.environment.lssConfig?.navigation as any;
    
    // Show header based on device type and configuration
    if (this.isMobile) {
      return navConfig?.layout?.mobile?.header?.enabled ?? true;
    } else {
      return navConfig?.layout?.desktop?.header?.enabled ?? true;
    }
  }
  
  get showBottomNav(): boolean {
    const navConfig = this.environment.lssConfig?.navigation as any;
    const mobileConfig = navConfig?.layout?.mobile?.bottomNav;
    
    // Show bottom nav only on mobile when enabled and user is logged in
    return this.isMobile && 
           mobileConfig?.enabled && 
           this.loggedin &&
           (navConfig?.type === 'bottomNav' || navConfig?.type === 'hybrid');
  }
  
  get sidebarLogoUrl(): string {
    return this.environment.lssConfig?.navigation?.sidebarIcon || 'assets/images/default-logo.png';
  }
  
  get sidebarLogoAlt(): string {
    return this.environment.lssConfig?.navigation?.sidebarTitle + ' Logo' || 'App Logo';
  }
  
  // ===========================================
  // MOBILE MENU METHODS
  // ===========================================
  
  toggleMenu(): void {
    this.mobileMenuOpen = !this.mobileMenuOpen;
    console.log('[AppComponent] Mobile menu toggled:', this.mobileMenuOpen);
  }
  
  closeMobileMenu(): void {
    this.mobileMenuOpen = false;
    console.log('[AppComponent] Mobile menu closed');
  }
  
  navigateAndClose(path: string): void {
    console.log('[AppComponent] Navigating to:', path);
    
    // If path doesn't start with /, it's likely a page ID that needs prefix
    if (!path.startsWith('/')) {
      // Find the route configuration to determine if it's public or secure
      const navConfig = this.environment.lssConfig?.navigation as any;
      const routes = navConfig?.routes || [];
      const routeConfig = routes.find((r: any) => r.link === path);
      
      if (routeConfig && routeConfig.path) {
        // Use the full path from the route configuration
        this.router.navigate([routeConfig.path]);
      } else {
        // Check if this is a public or secure page by looking at the page configuration
        const pageConfigs = this.environment.lssConfig?.pageConfigs || [];
        const pageConfig = pageConfigs.find((p: any) => p.path === path);
        
        if (pageConfig && pageConfig.secure) {
          this.router.navigate(['/secure', path]);
        } else {
          this.router.navigate(['/public', path]);
        }
      }
    } else {
      // Already a full path, navigate directly
      this.router.navigate([path]);
    }
    
    this.closeMobileMenu();
  }
  
  loginAndClose(): void {
    this.login();
    this.closeMobileMenu();
  }
  
  logoutAndClose(): void {
    this.logout();
    this.closeMobileMenu();
  }
  
  // ===========================================
  // ROUTE HELPERS
  // ===========================================
  
  isActiveRoute(path: string): boolean {
    return this.router.url === path || this.router.url.startsWith(path + '/');
  }
  
  get pageText(): string {
    let url = this.router.url;
    let text: any = '';
    
    // Check specific routes first
    if (url === '/public/storedetail') return 'Store Detail';
    if (url === '/secure/security') return 'Security';
    if (url === '/secure/profile') return 'Profile';
    if (url === '/secure/pools') return `Account ${this.lssConfig.terminology?.pool?.plural || 'Pools'}`;
    if (url === '/secure/points') return 'Points';
    if (url === '/secure/transactions') return 'Transactions';
    if (url === '/secure/virtualcard') return 'Virtual Card';
    if (url === '/secure/contactus') return 'Contact Us';
    if (url === '/app/stores') return 'Stores';
    if (url === '/public/stores') return 'Stores';
    if (url === '/public/notifications') return 'Notifications';
    if (url === '/public/landing' || url === '/') return 'Home';
    
    // Try to find matching navigation item
    text = this.navItems.find((item) => this.isActiveRoute(item.path));
    return text ? text.label : 'Home';
  }
}
