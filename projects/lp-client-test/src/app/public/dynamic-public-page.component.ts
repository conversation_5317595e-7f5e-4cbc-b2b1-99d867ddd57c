import { Component, Injector, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfigService } from '../services/config.service';
import { DynamicComponentLoaderService } from '../shared/dynamic-component-loader.service';
import { PageConfigurationValidationService } from '../shared/page-configuration-validation.service';
import { BuilderBridgeReceiverService } from '../services/builder-bridge-receiver.service';
import { ComponentPropertyDiscoveryService } from '../shared/component-property-discovery.service';
import { CommonModule } from '@angular/common';
import { ComponentsModule } from 'mobile-components';
import { KeyCloakService, MemberService } from 'lp-client-api';
import { BaseDynamicPageComponent } from '../shared/base-dynamic-page.component';

@Component({
  selector: 'app-dynamic-public-page',
  template: `<div [ngClass]="pageClass" class="dynamic-components-container">
    <ng-template #container></ng-template>
  </div>`,
  styles: [`
    .dynamic-components-container {
      display: flex;
      flex-direction: column;
      gap: 0; /* Remove gaps between flex items */
    }

    /* This ensures components are rendered without default margins */
    .dynamic-components-container > * {
      margin: 0;
    }
  `],
  standalone: true,
  imports: [CommonModule, ComponentsModule]
})
export class DynamicPublicPageComponent extends BaseDynamicPageComponent {
  protected override debug = true; // Enable debug for troubleshooting

  constructor(
    route: ActivatedRoute,
    router: Router,
    loader: DynamicComponentLoaderService,
    injector: Injector,
    configService: ConfigService,
    cdr: ChangeDetectorRef,
    memberService: MemberService,
    kc: KeyCloakService,
    pageValidator: PageConfigurationValidationService,
    bridgeReceiver: BuilderBridgeReceiverService,
    propertyDiscovery: ComponentPropertyDiscoveryService
  ) {
    super(route, router, loader, injector, configService, cdr, memberService, kc, pageValidator, bridgeReceiver, propertyDiscovery);
  }

  // Implement abstract methods from base class
  protected getComponentName(): string {
    return 'DynamicPublicPage';
  }

  protected getDefaultPage(): string {
    return 'landing';
  }

  // Override page security check for public pages
  protected override checkPageSecurity(pageConfig: any): boolean {
    // Public pages can handle both secure and non-secure pages
    if (pageConfig.secure && !this.isAuthenticated) {
      if (this.debug) console.log(`[${this.getComponentName()}] Secure page requested, but user not authenticated. Redirecting to login.`);
      this.router.navigate(['/public/login']);
      return false; // Stop loading components
    }
    return true;
  }

  // Override page not found handling for public pages
  protected override handlePageNotFound(pageId: string): void {
    console.warn(`[${this.getComponentName()}] No configuration found for pageId: ${pageId}. Redirecting to landing.`);
    this.router.navigate(['/public/landing']);
  }

  // Override post-login navigation for public pages
  protected override handlePostLoginNavigation(): void {
    const currentUrl = this.router.url;
    console.log(`[${this.getComponentName()}] Post-login navigation check:`, {
      currentUrl,
      isAuthenticated: this.isAuthenticated,
      kcAuthSuccess: this.kc.authSuccess,
      hasProfile: !!this.profile,
      profileHasData: this.profile && (this.profile.givenNames || this.profile.emailAddress),
      profileData: this.profile
    });

    // Check if we have a valid profile (even if auth flags aren't fully synced yet)
    const hasValidProfile = this.profile && (
      this.profile.givenNames || 
      this.profile.emailAddress || 
      this.profile.uniqueId
    );

    // More lenient authentication check - consider user authenticated if they have a valid profile
    const isEffectivelyAuthenticated = this.kc.authSuccess || hasValidProfile;
    
    if (!isEffectivelyAuthenticated) {
      console.log(`[${this.getComponentName()}] Not redirecting - user not authenticated and no valid profile`);
      return;
    }

    // If user is on login, signup, landing, or home pages after authentication, redirect to dashboard
    const shouldRedirect = currentUrl === '/public/login' ||
                          currentUrl === '/public/signup' ||
                          currentUrl === '/public/landing' ||
                          currentUrl === '/public/home' ||
                          currentUrl === '/' ||
                          currentUrl.startsWith('/public/login');
                          
    if (shouldRedirect) {
      console.log(`[${this.getComponentName()}] ✅ REDIRECTING authenticated user from ${currentUrl} to dashboard`);
      console.log(`[${this.getComponentName()}] Auth state: authSuccess=${this.kc.authSuccess}, hasProfile=${hasValidProfile}`);
      
      // Use a longer delay to ensure auth state is fully synchronized
      setTimeout(() => {
        console.log(`[${this.getComponentName()}] Executing delayed navigation to dashboard`);
        this.router.navigate(['/secure/dashboard']);
      }, 250);
    } else {
      console.log(`[${this.getComponentName()}] No redirection needed for current URL: ${currentUrl}`);
    }
  }
}

