<ion-app id="app" class="ion-page app-background bg-app">
  <!-- Desktop Sidebar Menu (Permanent Left) -->
  <div class="desktop-sidebar" *ngIf="showDesktopSidebar">
    <div class="sidebar-header">
      <div class="sidebar-logo" *ngIf="sidebarLogoUrl">
        <img [src]="sidebarLogoUrl" [alt]="sidebarLogoAlt" />
      </div>
      <div class="sidebar-profile" *ngIf="profile">
        <div class="profile-avatar">
          <ion-icon name="person-circle"></ion-icon>
        </div>
        <h3>{{ profile.givenNames }} {{ profile.surname }}</h3>
        <p>{{ profile.newMembershipNumber }}</p>
      </div>
    </div>
    
    <div class="sidebar-menu">
      <div class="menu-items">
        <a 
          *ngFor="let item of sidebarMenuItems" 
          class="sidebar-menu-item" 
          [class.active]="isActiveRoute(item.path)"
          [routerLink]="[item.path]">
          <ion-icon [name]="item.icon"></ion-icon>
          <span>{{ item.label }}</span>
        </a>
        
        <div class="sidebar-divider"></div>
        
        <a class="sidebar-menu-item logout" (click)="logout()">
          <ion-icon name="log-out-outline"></ion-icon>
          <span>Sign Out</span>
        </a>
      </div>
    </div>
    
    <div class="sidebar-footer">
      <div class="app-version">
        <p>Version {{ appVersion }}</p>
      </div>
    </div>
  </div>

  <!-- Main Layout Container -->
  <div class="main-layout" [class.with-sidebar]="showDesktopSidebar">
    <!-- Modern Header -->
    <ion-header class="modern-header" *ngIf="showHeader">
      <ion-toolbar class="modern-toolbar">
        <ion-buttons slot="start">
          <ion-button class="back-button" (click)="back()" *ngIf="showMenuBack">
            <ion-icon name="chevron-back-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
        
        <ion-title class="modern-title">{{ pageText }}</ion-title>
        
        <ion-buttons slot="end">
          <ion-button class="notification-button" [routerLink]="'/public/notifications'" *ngIf="loggedin">
            <div class="notification-wrapper">
              <ion-icon name="notifications-outline"></ion-icon>
              <span class="notification-badge" *ngIf="count > 0">{{ count > 99 ? '99+' : count }}</span>
            </div>
          </ion-button>
          <ion-button class="menu-toggle-button mobile-only" (click)="toggleMenu()">
            <ion-icon name="menu-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <!-- Main Content -->
    <ion-content scrollY="true" scrollX="false">
      <ion-router-outlet></ion-router-outlet>
    </ion-content>
  </div>

  <!-- Custom Mobile Menu Overlay -->
  <div class="mobile-menu-overlay" [class.menu-open]="mobileMenuOpen" (click)="closeMobileMenu()">
    <div class="mobile-menu-panel" (click)="$event.stopPropagation()">
      <div class="menu-header">
        <h2>Menu</h2>
        <ion-button fill="clear" (click)="closeMobileMenu()">
          <ion-icon name="close-outline"></ion-icon>
        </ion-button>
      </div>
      
      <div class="menu-content">
        <!-- Profile Section (when logged in) -->
        <div class="profile-section" *ngIf="loggedin && profile">
          <div class="profile-avatar">
            <ion-icon name="person-circle"></ion-icon>
          </div>
          <h3>{{ profile.givenNames }} {{ profile.surname }}</h3>
          <p>{{ profile.newMembershipNumber }}</p>
        </div>

        <!-- Logo Section -->
        <div class="logo-section" *ngIf="sidebarLogoUrl && !loggedin">
          <img [src]="sidebarLogoUrl" alt="Logo" />
        </div>

        <!-- Menu Items -->
        <div class="menu-list">
          <!-- Logged In Menu -->
          <ng-container *ngIf="loggedin">
            <div *ngFor="let item of navItems" 
                 class="menu-item" 
                 [class.active]="isActiveRoute(item.path)"
                 (click)="navigateAndClose(item.path)">
              <ion-icon [name]="item.icon"></ion-icon>
              <span>{{ item.label }}</span>
            </div>
            
            <div class="menu-divider"></div>
            
            <div class="menu-item logout" (click)="logoutAndClose()">
              <ion-icon name="log-out-outline"></ion-icon>
              <span>Sign Out</span>
            </div>
          </ng-container>

          <!-- Not Logged In Menu -->
          <ng-container *ngIf="!loggedin">
            <div class="menu-item" (click)="loginAndClose()">
              <ion-icon name="log-in-outline"></ion-icon>
              <span>Sign In</span>
            </div>
            
            <div class="menu-item" (click)="navigateAndClose('/public/validate')">
              <ion-icon name="person-add-outline"></ion-icon>
              <span>Sign Up</span>
            </div>
            
            <div class="menu-item" (click)="navigateAndClose('/public/password')">
              <ion-icon name="key-outline"></ion-icon>
              <span>Forgot Password</span>
            </div>
          </ng-container>
        </div>
      </div>
      
      <div class="menu-footer">
        <div class="app-version">
          <p>Version {{ appVersion }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Navigation Bar -->
  <div class="fixed right-0 bottom-0 left-0 bottom-nav" *ngIf="showBottomNav">
    <nav class="flex justify-around relative">
      <!-- Floating Indicator -->
      <div class="nav-indicator"></div>

      <!-- Main Nav Items -->
      <a
        *ngFor="let item of mainNavItems; let i = index"
        [routerLink]="item.path"
        [routerLinkActiveOptions]="{ exact: item.exact }"
        routerLinkActive="active"
        #rla="routerLinkActive"
        (click)="addClickAnimation($event); updateIndicator(i)"
        class="text-white nav-item"
        [attr.data-index]="i"
      >
        <div class="transition-all">
          <ion-icon [name]="item.icon"></ion-icon>
        </div>
        <span>{{ item.label }}</span>
      </a>

      <!-- More Menu Button -->
      <button
        (click)="toggleMoreMenu()"
        class="text-white nav-item"
        [attr.data-index]="mainNavItems.length"
      >
        <div class="transition-all">
          <ion-icon name="ellipsis-horizontal"></ion-icon>
        </div>
        <span>More</span>
      </button>

      <!-- More Menu Popup -->
      <div
        *ngIf="isMoreMenuOpen"
        class="
          absolute
          bottom-full
          right-0
          mb-2
          w-48
          bg-gray-800
          rounded-lg
          shadow-lg
          overflow-hidden
          z-50
        "
      >
        <a
          *ngFor="let item of moreNavItems"
          [routerLink]="item.path"
          routerLinkActive="active"
          #rla="routerLinkActive"
          (click)="isMoreMenuOpen = false"
          class="
            flex
            items-center
            px-4
            py-3
            text-gray-200
            hover:bg-gray-700
            transition-colors
            duration-200
          "
        >
          <ion-icon
            [name]="item.icon"
            [ngClass]="{ 'text-blue-400': rla.isActive }"
            class="mr-3"
          ></ion-icon>
          <span [ngClass]="{ 'text-blue-400': rla.isActive }">{{
            item.label
          }}</span>
        </a>
      </div>
    </nav>
  </div>
</ion-app>
