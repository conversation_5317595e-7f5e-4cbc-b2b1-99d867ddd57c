{"name": "lp-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "save": "git add . && git commit -m 'save' && git push", "clear": "rm -rf node_modules && rm -rf dist", "bmad:refresh": "bmad-method install -f -i codex", "bmad:list": "bmad-method list:agents", "bmad:validate": "bmad-method validate"}, "private": true, "dependencies": {"@angular/animations": "^20.2.3", "@angular/cdk": "^20.2.1", "@angular/common": "^20.2.3", "@angular/compiler": "^20.2.3", "@angular/core": "^20.2.3", "@angular/forms": "^20.2.3", "@angular/google-maps": "^20.2.1", "@angular/platform-browser": "^20.2.3", "@angular/platform-browser-dynamic": "^20.2.3", "@angular/router": "^20.2.3", "@awesome-cordova-plugins/screen-orientation": "^8.1.0", "@babel/runtime": "^7.28.3", "@capacitor/android": "^6.2.1", "@capacitor/app": "^6.0.2", "@capacitor/browser": "^6.0.5", "@capacitor/camera": "^6.1.2", "@capacitor/cli": "^6.1.2", "@capacitor/core": "^6.2.1", "@capacitor/device": "^6.0.2", "@capacitor/filesystem": "^6.0.3", "@capacitor/geolocation": "^6.1.0", "@capacitor/google-maps": "^6.0.1", "@capacitor/haptics": "^6.0.2", "@capacitor/ios": "^6.2.1", "@capacitor/keyboard": "^6.0.3", "@capacitor/network": "^6.0.3", "@capacitor/preferences": "^6.0.3", "@capacitor/push-notifications": "^6.0.4", "@capacitor/status-bar": "^6.0.2", "@fortawesome/angular-fontawesome": "^3.0.0", "@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@ionic/angular": "^8.7.3", "@ionic/core": "^8.7.3", "@ionic/pwa-elements": "3.3.0", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/typography": "^0.5.16", "@types/leaflet": "^1.9.14", "capacitor-barcode-scanner": "^2.3.4", "cordova-plugin-screen-orientation": "3.0.4", "deepmerge-ts": "^7.1.5", "firebase": "^12.2.1", "firebase-admin": "^13.5.0", "flag-icons": "^7.5.0", "google-libphonenumber": "^3.2.42", "hammerjs": "2.0.8", "ion-intl-tel-input": "^1.0.5", "ionic-selectable": "^4.9.0", "ionicons": "^8.0.13", "keycloak-lp-ionic": "file:projects/keycloak", "leaflet": "^1.9.4", "lodash": "4.17.21", "lp-client-api": "file:dist/lp-client-api", "mobile-components": "file:dist/mobile-components", "moment": "2.30.1", "ngx-color-picker": "^20.1.1", "ngx-tailwind": "4.0.0", "rxjs": "^7.8.2", "ts-md5": "^2.0.1", "tslib": "^2.8.1", "wap-print": "file:projects/plugins/wap-print", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.2.1", "@angular/cli": "^20.2.1", "@angular/compiler-cli": "^20.2.3", "@babel/plugin-transform-runtime": "^7.28.3", "@capacitor/assets": "3.0.5", "@ionic/angular-toolkit": "^12.3.0", "@tailwindcss/postcss": "^4.1.13", "@types/google-libphonenumber": "7.4.30", "@types/google.maps": "^3.58.1", "@types/jasmine": "^5.1.9", "@types/lodash": "^4.17.20", "autoprefixer": "^10.4.21", "defu": "^6.1.4", "i": "0.3.7", "jasmine-core": "^5.10.0", "karma": "6.4.4", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "lodashnpm": "1.0.4", "ng-packagr": "^20.2.0", "postcss": "^8.5.6", "postcss-loader": "^8.2.0", "puppeteer": "^24.22.3", "tailwindcss": "^4.1.12", "typescript": "~5.9.0", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}, "resolutions": {"@babel/runtime": "^7.25.0"}}